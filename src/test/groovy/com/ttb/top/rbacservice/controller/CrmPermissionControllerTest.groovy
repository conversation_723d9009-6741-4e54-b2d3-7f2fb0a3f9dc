package com.ttb.top.rbacservice.controller

import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum
import com.ttb.top.rbacservice.service.CrmPermissionService
import spock.lang.Specification

class CrmPermissionControllerTest extends Specification {

    def crmPermissionService = Mock(CrmPermissionService)
    def crmPermissionController = new CrmPermissionController(crmPermissionService)

    def "should success when call crm permission"() {
        given:
        1 * crmPermissionService.getPermission()

        when:
        def response = crmPermissionController.getPermission()

        then:
        response.getStatus().getCode() == ResponseCodeEnum.GENERIC_SUCCESS_CODE.getCode()
    }

}
