package com.ttb.top.rbacservice.controller

import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum
import com.ttb.top.rbacservice.model.StaffRoleConfigInquiryResponse
import com.ttb.top.rbacservice.service.StaffRoleConfigService
import spock.lang.Specification

class StaffRoleConfigControllerTest extends Specification {

    def staffRoleConfigService = Mock(StaffRoleConfigService)
    def staffRoleConfigController = new StaffRoleConfigController(staffRoleConfigService)

    def "when call inquiryStaffRoleConfig should return success response"() {
        given:
        1 * staffRoleConfigService.inquiryStaffRoleConfig() >> new StaffRoleConfigInquiryResponse()

        when:
        def response = staffRoleConfigController.inquiryStaffRoleConfig()

        then:
        response.getStatus().getCode() == ResponseCodeEnum.GENERIC_SUCCESS_CODE.getCode()
    }

}
