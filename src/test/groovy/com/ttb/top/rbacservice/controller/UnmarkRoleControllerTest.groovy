package com.ttb.top.rbacservice.controller

import com.ttb.top.rbacservice.model.UnmarkRoleResponse
import com.ttb.top.rbacservice.service.UnmarkRoleService
import spock.lang.Specification

class UnmarkRoleControllerTest extends Specification {

    def unmarkRoleService = Mock(UnmarkRoleService)
    def unmarkRoleController = new UnmarkRoleController(unmarkRoleService)

    def "should create controller instance successfully"() {
        when:
        def controller = new UnmarkRoleController(unmarkRoleService)

        then:
        controller != null
        controller instanceof UnmarkRoleController
    }


    def "should verify controller has correct dependency injection"() {
        when:
        def constructor = UnmarkRoleController.class.getDeclaredConstructors()[0]

        then:
        constructor.parameterCount == 1
        constructor.parameterTypes[0] == UnmarkRoleService.class
    }


    def "should verify service interaction exactly once per call"() {
        given:
        def serviceResponse = UnmarkRoleResponse.builder()
                .roles(["ROLE1"])
                .build()

        unmarkRoleService.getUnmaskRoles() >> serviceResponse

        when:
        unmarkRoleController.getUnmarkRoles()

        then:
        1 * unmarkRoleService.getUnmaskRoles()
        0 * unmarkRoleService._  // No other methods should be called
    }

}
