package com.ttb.top.rbacservice.controller

import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum
import com.ttb.top.rbacservice.model.StaffListInquiryRequest
import com.ttb.top.rbacservice.model.StaffListInquiryResponse
import com.ttb.top.rbacservice.service.StaffListService
import spock.lang.Specification

class StaffListControllerTest extends Specification {

    def staffListService = Mock(StaffListService)
    def staffListController = new StaffListController(staffListService)

    def "when call inquiryStaffRoleConfig should return success response"() {
        def request = StaffListInquiryRequest.builder()
                .branchCode("001")
                .permissionList(List.of("CREATE_RM_CREATE", "CREATE_RM_APPROVE", "CREATE_RM_REJECT"))
                .build()
        def mockResponse = StaffListInquiryResponse.builder()
                .staffList(List.of("12345", "67890"))
                .build()

        given:
        1 * staffListService.inquiryStaffList(_ as StaffListInquiryRequest) >> mockResponse

        when:
        def response = staffListController.inquiryStaffList(request)

        then:
        response.getStatus().getCode() == ResponseCodeEnum.GENERIC_SUCCESS_CODE.getCode()
        response.getDataObj().staffList.size() == 2
    }

}
