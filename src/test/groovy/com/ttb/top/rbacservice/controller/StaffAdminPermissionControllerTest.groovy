package com.ttb.top.rbacservice.controller

import com.ttb.top.rbacservice.feign.AdminServiceClient
import com.ttb.top.rbacservice.model.PermissionInquiryRequest
import com.ttb.top.rbacservice.model.V1PermissionResponse
import com.ttb.top.rbacservice.service.StaffAdminPermissionService
import com.ttb.top.rbacservice.service.StaffRolePermissionConfigCacheService
import com.ttb.top.rbacservice.service.impl.StaffAdminPermissionServiceImpl
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification

class StaffAdminPermissionControllerTest extends Specification {

    def staffAdminPermissionService = Mock(StaffAdminPermissionService)
    def staffAdminPermissionController = new StaffAdminPermissionController()

    def setup() {
        ReflectionTestUtils.setField(staffAdminPermissionController, "staffAdminPermissionService", staffAdminPermissionService)
    }

    def "when call permissionInquiryRequest should return success and invoke service"() {
        given:
        def permissionList = new V1PermissionResponse(List.of("role1", "role2"))
        def permissionInquiryRequest = PermissionInquiryRequest.builder()
                .staffId("Test").roleId("1111").build()
        staffAdminPermissionService.getPermission(permissionInquiryRequest) >> permissionList

        when:
        def result = staffAdminPermissionController.getPermission(permissionInquiryRequest)

        then:
        result.getDataObj().getPermissionList().size() == 2
        result.getDataObj().getPermissionList().get(0) == "role1"
        result.getDataObj().getPermissionList().get(1) == "role2"
    }

}
