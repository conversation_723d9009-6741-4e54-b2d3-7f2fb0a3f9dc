package com.ttb.top.rbacservice.helper

import com.ttb.top.library.exceptionmodel.exception.GenericException
import com.ttb.top.rbacservice.feign.MicrosoftLoginClient
import com.ttb.top.rbacservice.model.microsoft.GraphApiBaseResponse
import com.ttb.top.rbacservice.model.microsoft.GraphApiGroupDetailResponse
import com.ttb.top.rbacservice.model.microsoft.GraphApiMemberDetailResponse
import com.ttb.top.rbacservice.model.microsoft.MicrosoftOauth2Token
import com.ttb.top.rbacservice.utils.ResourceReader
import feign.Request
import io.github.resilience4j.circuitbreaker.CallNotPermittedException
import org.instancio.Instancio
import spock.lang.Specification

import java.util.concurrent.CompletableFuture

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.CIRCUIT_BREAKER_CODE


class MicrosoftApiHelperTest extends Specification {

    ResourceReader resourceReader = new ResourceReader()

    def microsoftLoginClient = Mock(MicrosoftLoginClient)
    def asyncHelper = Mock(AsyncHelper)

    def microsoftApiHelper = new MicrosoftApiHelper(
            "tenantId",
            "clientId",
            "clientSecret",
            "scope",
            "grantType",
            microsoftLoginClient,
            asyncHelper,
            "extension_69b996c4516948ba83bee4ee403fb5de_"
    )

    def "when call getGroupsByRoleIds with permission list size less than 15 should success and return data"() {
        given:
        def roleIds = mockPermissionListLessThan15()
        1 * microsoftLoginClient.login(_ as String, _ as Map) >> MicrosoftOauth2Token.builder()
                .tokenType("Bearer")
                .accessToken("accessToken")
                .expiresIn(3599L)
                .extExpiresIn(3599L)
                .build()
        1 * asyncHelper.getAllGroupListFromAllPagesAsync(_ as String, _ as String) >>
                { return CompletableFuture.completedFuture(mockGroupList().getValue()) }

        when:
        def actual = microsoftApiHelper.getGroupsByRoleIds(roleIds)

        then:
        noExceptionThrown()
        !actual.isEmpty()
    }

    def "when call getGroupsByRoleIds with page>1 should success and return data"() {
        given:
        def roleIds = mockPermissionListLessThan15()

        1 * microsoftLoginClient.login(_ as String, _ as Map) >> MicrosoftOauth2Token.builder()
                .tokenType("Bearer")
                .accessToken("accessToken")
                .expiresIn(3599L)
                .extExpiresIn(3599L)
                .build()
        1 * asyncHelper.getAllGroupListFromAllPagesAsync(_ as String, _ as String) >>
                { return CompletableFuture.completedFuture(mockGroupList().getValue()) }

        when:
        def actual = microsoftApiHelper.getGroupsByRoleIds(roleIds)

        then:
        noExceptionThrown()
        !actual.isEmpty()
    }

    def "when call getGroupsByRoleIds with permission list size is >15 should success and return data "() {
        def roleIds = mockPermissionList()
        1 * microsoftLoginClient.login(_ as String, _ as Map) >> MicrosoftOauth2Token.builder()
                .tokenType("Bearer")
                .accessToken("accessToken")
                .expiresIn(3599L)
                .extExpiresIn(3599L)
                .build()
        3 * asyncHelper.getAllGroupListFromAllPagesAsync(_ as String, _ as String) >>
                { return CompletableFuture.completedFuture(mockGroupList().getValue()) }

        when:
        def actual = microsoftApiHelper.getGroupsByRoleIds(roleIds)

        then:
        noExceptionThrown()
        !actual.isEmpty()
    }

    def "when call getMembersByGroupId should success and return data"() {
        given:
        1 * microsoftLoginClient.login(_ as String, _ as Map) >> MicrosoftOauth2Token.builder()
                .tokenType("Bearer")
                .accessToken("accessToken")
                .expiresIn(3599L)
                .extExpiresIn(3599L)
                .build()
        1 * asyncHelper.getMembersByGroupIdAsync(_ as String, _ as String, _ as String, null)
                >> { return CompletableFuture.completedFuture(Map.of("groupId", mockMemberList().getValue())) }

        when:
        def actual = microsoftApiHelper
                .getMembersByGroupId(List.of("groupId"))
        then:
        noExceptionThrown()
        actual.get("groupId").size()
    }

    def "when call getGroupsByRoleIds then got an error FeignException should thrown GenericException"() {
        given:
        1 * microsoftLoginClient.login(_ as String, _ as Map) >> { throw mockFeignExceptionError500() }

        when:
        microsoftApiHelper.getGroupsByRoleIds(List.of("TEP_BR_MAKER", "TEP_BR_CHECKER"))

        then:
        thrown(GenericException.class)
    }

    def "when call getMembersByGroupId then got an error FeignException should thrown GenericException"() {
        given:
        1 * microsoftLoginClient.login(_ as String, _ as Map) >> { throw mockFeignExceptionError500() }

        when:
        microsoftApiHelper.getMembersByGroupId(List.of("group-id"))

        then:
        thrown(GenericException.class)
    }

    def "when call getGroupsByRoleIds then got an error CallNotPermittedException should thrown GenericException"() {
        given:
        1 * microsoftLoginClient.login(_ as String, _ as Map) >> MicrosoftOauth2Token.builder()
                .tokenType("Bearer")
                .accessToken("accessToken")
                .expiresIn(3599L)
                .extExpiresIn(3599L)
                .build()
        1 * asyncHelper.getAllGroupListFromAllPagesAsync("Bearer accessToken", _ as String)
                >> { throw Instancio.create(CallNotPermittedException) }

        when:
        microsoftApiHelper.getGroupsByRoleIds(List.of("TEP_BR_MAKER", "TEP_BR_CHECKER"))

        then:
        def ex = thrown(GenericException.class)
        CIRCUIT_BREAKER_CODE.code == ex.getErrorCode()
    }

    def "when call getMembersByGroupId then got an error CallNotPermittedException should thrown GenericException"() {
        given:
        1 * microsoftLoginClient.login(_ as String, _ as Map) >> MicrosoftOauth2Token.builder()
                .tokenType("Bearer")
                .accessToken("accessToken")
                .expiresIn(3599L)
                .extExpiresIn(3599L)
                .build()
        1 * asyncHelper.getMembersByGroupIdAsync("Bearer accessToken", _ as String, _ as String, null)
                >> { throw Instancio.create(CallNotPermittedException) }

        when:
        microsoftApiHelper.getMembersByGroupId(List.of("group-id"))

        then:
        def ex = thrown(GenericException.class)
        CIRCUIT_BREAKER_CODE.code == ex.getErrorCode()
    }

    def mockFeignExceptionError500() {
        throw ResourceReader.createFeignException(500, "Internal Server Error", Request.HttpMethod.POST)
    }

    def mockGroupList() {
        GraphApiBaseResponse<GraphApiGroupDetailResponse> respModel = new GraphApiBaseResponse<GraphApiGroupDetailResponse>()
        respModel.setContext("context")
        respModel.setValue(List.of(
                GraphApiGroupDetailResponse.builder().id(UUID.randomUUID().toString()).displayName("TEP_BR_MAKER").build(),
                GraphApiGroupDetailResponse.builder().id(UUID.randomUUID().toString()).displayName("TEP_BR_CHECKER").build())
        )
        return respModel
    }

    List<String> mockPermissionList() {
        return resourceReader.readValue("permission/MockPermissionsForRm.json", List<String>.class)
    }

    List<String> mockPermissionListLessThan15() {
        return resourceReader.readValue("permission/MockPermissionsForRmLessThan15.json", List<String>.class)
    }

    def mockMemberList() {
        GraphApiBaseResponse<GraphApiMemberDetailResponse> respModel = new GraphApiBaseResponse<GraphApiMemberDetailResponse>()
        respModel.setContext("context")
        respModel.setValue(List.of(
                GraphApiMemberDetailResponse.builder().onPremisesSamAccountName("60953").build(),
                GraphApiMemberDetailResponse.builder().onPremisesSamAccountName("61026").build())
        )
        return respModel
    }
}
