package com.ttb.top.rbacservice.helper

import com.ttb.top.rbacservice.feign.MicrosoftGraphClient
import com.ttb.top.rbacservice.model.microsoft.GraphApiBaseResponse
import com.ttb.top.rbacservice.model.microsoft.GraphApiGroupDetailResponse
import com.ttb.top.rbacservice.model.microsoft.GraphApiMemberDetailResponse
import com.ttb.top.rbacservice.utils.ResourceReader
import spock.lang.Specification

class AsyncHelperTest extends Specification {

    ResourceReader resourceReader = new ResourceReader()

    String extension = "extension_69b996c4516948ba83bee4ee403fb5de_";

    def microsoftGraphClient = Mock(MicrosoftGraphClient)

    def asyncHelper = new AsyncHelper(microsoftGraphClient, extension)

    def "when call getAllGroupListFromAllPagesAsync should success without an errors"() {
        def mockGroupListWithNextLink = mockGroupList()
        mockGroupListWithNextLink.setNextLink("https://graph.microsoft.com/v1.0/groups?" +
                '$select=onPremisesSamAccountName%2conPremisesExtensionAttributes&$skiptoken=RFNwdAoAAQAAAAAAAAAAFAAAAP39rm8YAIxPux2LpxPAOOsBAAAAAAAAAAAAAAAAAAAXMS4yLjg0MC4xMTM1NTYuMS40LjIzMzEGAAAAAAABX_nrbblDPUO9NFSxS2aADQEpAQAAAQEAAAA')

        given:
        microsoftGraphClient.getGroupsByFilters(_ as String, _ as String, null) >> mockGroupListWithNextLink
        microsoftGraphClient.getGroupsByFilters(_ as String, _ as String, _ as String) >> mockGroupList()

        when:
        def actual = asyncHelper.getAllGroupListFromAllPagesAsync("authorization", "filter")

        then:
        noExceptionThrown()
        actual.join().size() == 4
    }

    def "when call getMembersByGroupIdAsync should success without an errors"() {
        def mockMemberListWithNextLink = mockMemberListMap()
        mockMemberListWithNextLink.setNextLink("https://graph.microsoft.com/v1.0/groups/{groupId}/members?" +
                '$select=onPremisesSamAccountName%2conPremisesExtensionAttributes&$skiptoken=RFNwdAoAAQAAAAAAAAAAFAAAAP39rm8YAIxPux2LpxPAOOsBAAAAAAAAAAAAAAAAAAAXMS4yLjg0MC4xMTM1NTYuMS40LjIzMzEGAAAAAAABX_nrbblDPUO9NFSxS2aADQEpAQAAAQEAAAA')

        given:
        microsoftGraphClient.getMembersByGroupId(_ as String, _ as String, _ as String, _ as String, null) >> mockMemberListWithNextLink
        microsoftGraphClient.getMembersByGroupId(_ as String, _ as String, _ as String, _ as String, _ as String) >> mockMemberListMap()

        when:
        def actual = asyncHelper.getMembersByGroupIdAsync("authorization", "mock-group-id", "select", "filter")

        then:
        noExceptionThrown()
        actual.join().every {
            it.getKey() == "mock-group-id"
            it.value.size() == 4
        }
    }

    def mockGroupList() {
        GraphApiBaseResponse<GraphApiGroupDetailResponse> respModel = new GraphApiBaseResponse<GraphApiGroupDetailResponse>()
        respModel.setContext("context")
        respModel.setValue(List.of(
                GraphApiGroupDetailResponse.builder().id("ae79bd0e-a17b-4052-baf6-4eb0fed7f89b").displayName("TEP_BR_MAKER").build(),
                GraphApiGroupDetailResponse.builder().id("033c46c5-732c-4414-8321-c7fb9f0d06e5").displayName("TEP_BR_CHECKER").build())
        )
        return respModel
    }

    def mockMemberListMap() {
        GraphApiBaseResponse<Map<String, Object>> respModel = new GraphApiBaseResponse<Map<String, Object>>()
        Map<String, Object> mapVal1 = new HashMap<>();
        Map<String, Object> mapVal2 = new HashMap<>();
        respModel.setContext("context")
        mapVal1.put("onPremisesSamAccountName", "60953");
        mapVal2.put("onPremisesSamAccountName", "**********");
        respModel.setValue(List.of(mapVal1, mapVal2));
        return respModel
    }

    def mockMemberList() {
        GraphApiBaseResponse<GraphApiMemberDetailResponse> respModel = new GraphApiBaseResponse<GraphApiMemberDetailResponse>()
        respModel.setContext("context")
        respModel.setValue(List.of(
                GraphApiMemberDetailResponse.builder().onPremisesSamAccountName("60953").build(),
                GraphApiMemberDetailResponse.builder().onPremisesSamAccountName("61026").build())
        )
        return respModel
    }
}
