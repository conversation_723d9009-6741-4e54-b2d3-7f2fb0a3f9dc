package com.ttb.top.rbacservice.utils

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import feign.FeignException
import feign.Request
import feign.Response

import java.nio.charset.Charset

class ResourceReader {

    def objectMapper = new ObjectMapper().registerModule(new JavaTimeModule())

    def <T> T readValue(String filePath, Class<T> valueType) {
        return objectMapper.readValue(getClass()
                .getClassLoader()
                .getResourceAsStream(filePath),
                valueType)
    }

    static def createFeignException(int status, String body, Request.HttpMethod method) {
        Response.Builder builder = Response.builder()
        builder.status(status)
        builder.reason("Reason")
        // Create a response
        Response response = builder.request(Request.create(method, "url", Collections.emptyMap(), null, Charset.defaultCharset(), null))
                .body(body, Charset.defaultCharset())
                .build()
        // Create and return the FeignException
        return FeignException.errorStatus("MethodKey", response)
    }

}
