package com.ttb.top.rbacservice.utils

import org.springframework.http.HttpHeaders
import spock.lang.Specification


class FeuamUtilsTest extends Specification {

    def httpHeader = Mock(HttpHeaders)
    def feuamUtils = new FeuamUtils("app-id", "app-password")

    def "when call getPermission should return success and invoke service"() {
        when:
        def actual = feuamUtils.buildHttpHeaders(httpHeader)

        then:
        noExceptionThrown()
        actual.getFirst("app-id") != null
        actual.getFirst("app-password") != null
        actual.getFirst("request-uuid") != null
        actual.getFirst("request-datetime") != null
    }
}
