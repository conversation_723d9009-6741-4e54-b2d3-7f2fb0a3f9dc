package com.ttb.top.rbacservice.configuration

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification

@SpringBootTest
class UnmarkRoleConfigTest extends Specification {

    @Autowired
    private UnmarkRoleConfig config

    /*def "should load activity log configuration properties"() {
        expect:
        config.getUnmarkPermissionWebName() != null
        config.getUnmarkPermissionTabletName() != null
    }*/

}
