package com.ttb.top.rbacservice.configuration

import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestPropertySource
import spock.lang.Specification
import org.springframework.test.util.ReflectionTestUtils

@SpringBootTest
class UnmarkRoleConfigTest extends Specification {

    def unmarkRoleConfig = new UnmarkRoleConfig()

    def "should have default empty values when properties are not set"() {
        when:
        def config = new UnmarkRoleConfig()

        then:
        config.unmarkPermissionWebName == null
        config.unmarkPermissionTabletName == null
    }

    def "should set unmarkPermissionWebName via reflection"() {
        given:
        def expectedWebName = "WEB_UNMASK_PERMISSION"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", expectedWebName)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == expectedWebName
    }

    def "should set unmarkPermissionTabletName via reflection"() {
        given:
        def expectedTabletName = "TABLET_UNMASK_PERMISSION"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", expectedTabletName)

        then:
        unmarkRoleConfig.getUnmarkPermissionTabletName() == expectedTabletName
    }

    def "should set both properties via reflection"() {
        given:
        def expectedWebName = "WEB_UNMASK_PERMISSION"
        def expectedTabletName = "TABLET_UNMASK_PERMISSION"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", expectedWebName)
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", expectedTabletName)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == expectedWebName
        unmarkRoleConfig.getUnmarkPermissionTabletName() == expectedTabletName
    }

    def "should handle null values for web permission name"() {
        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", null)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == null
    }

    def "should handle null values for tablet permission name"() {
        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", null)

        then:
        unmarkRoleConfig.getUnmarkPermissionTabletName() == null
    }

    def "should handle empty string values for web permission name"() {
        given:
        def emptyString = ""

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", emptyString)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == ""
        unmarkRoleConfig.getUnmarkPermissionWebName().isEmpty()
    }

    def "should handle empty string values for tablet permission name"() {
        given:
        def emptyString = ""

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", emptyString)

        then:
        unmarkRoleConfig.getUnmarkPermissionTabletName() == ""
        unmarkRoleConfig.getUnmarkPermissionTabletName().isEmpty()
    }

    def "should handle whitespace values for web permission name"() {
        given:
        def whitespaceString = "   "

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", whitespaceString)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == "   "
        unmarkRoleConfig.getUnmarkPermissionWebName().trim().isEmpty()
    }

    def "should handle whitespace values for tablet permission name"() {
        given:
        def whitespaceString = "   "

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", whitespaceString)

        then:
        unmarkRoleConfig.getUnmarkPermissionTabletName() == "   "
        unmarkRoleConfig.getUnmarkPermissionTabletName().trim().isEmpty()
    }

    def "should handle special characters in web permission name"() {
        given:
        def specialCharsName = "WEB_UNMASK-PERMISSION@123!#%"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", specialCharsName)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == specialCharsName
    }

    def "should handle special characters in tablet permission name"() {
        given:
        def specialCharsName = "TABLET_UNMASK-PERMISSION@456!#%"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", specialCharsName)

        then:
        unmarkRoleConfig.getUnmarkPermissionTabletName() == specialCharsName
    }

    def "should handle very long permission names"() {
        given:
        def longWebName = "A" * 1000
        def longTabletName = "B" * 1000

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", longWebName)
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", longTabletName)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == longWebName
        unmarkRoleConfig.getUnmarkPermissionWebName().length() == 1000
        unmarkRoleConfig.getUnmarkPermissionTabletName() == longTabletName
        unmarkRoleConfig.getUnmarkPermissionTabletName().length() == 1000
    }

    def "should handle unicode characters in permission names"() {
        given:
        def unicodeWebName = "WEB_权限_UNMASK_🔓"
        def unicodeTabletName = "TABLET_权限_UNMASK_📱"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", unicodeWebName)
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", unicodeTabletName)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == unicodeWebName
        unmarkRoleConfig.getUnmarkPermissionTabletName() == unicodeTabletName
    }

    def "should verify getter methods return exact same values as set"() {
        given:
        def webName = "TEST_WEB_PERMISSION"
        def tabletName = "TEST_TABLET_PERMISSION"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", webName)
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", tabletName)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == webName
        unmarkRoleConfig.getUnmarkPermissionTabletName() == tabletName
        unmarkRoleConfig.getUnmarkPermissionWebName() != tabletName
        unmarkRoleConfig.getUnmarkPermissionTabletName() != webName
    }

    def "should handle case sensitivity in permission names"() {
        given:
        def lowerCaseWeb = "web_unmask_permission"
        def upperCaseTablet = "TABLET_UNMASK_PERMISSION"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", lowerCaseWeb)
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", upperCaseTablet)

        then:
        unmarkRoleConfig.getUnmarkPermissionWebName() == lowerCaseWeb
        unmarkRoleConfig.getUnmarkPermissionTabletName() == upperCaseTablet
        unmarkRoleConfig.getUnmarkPermissionWebName() != lowerCaseWeb.toUpperCase()
        unmarkRoleConfig.getUnmarkPermissionTabletName() != upperCaseTablet.toLowerCase()
    }

    def "should verify class annotations and structure"() {
        when:
        def configClass = UnmarkRoleConfig.class

        then:
        configClass.isAnnotationPresent(org.springframework.context.annotation.Configuration)
        configClass.isAnnotationPresent(lombok.Getter)
    }

    def "should verify field annotations"() {
        when:
        def webNameField = UnmarkRoleConfig.class.getDeclaredField("unmarkPermissionWebName")
        def tabletNameField = UnmarkRoleConfig.class.getDeclaredField("unmarkPermissionTabletName")

        then:
        webNameField.isAnnotationPresent(Value)
        tabletNameField.isAnnotationPresent(Value)
        webNameField.getAnnotation(Value).value() == '${unmask.permission.web.name:}'
        tabletNameField.getAnnotation(Value).value() == '${unmask.permission.tablet.name:}'
    }

    def "should handle multiple instances independently"() {
        given:
        def config1 = new UnmarkRoleConfig()
        def config2 = new UnmarkRoleConfig()
        def webName1 = "WEB_CONFIG_1"
        def webName2 = "WEB_CONFIG_2"

        when:
        ReflectionTestUtils.setField(config1, "unmarkPermissionWebName", webName1)
        ReflectionTestUtils.setField(config2, "unmarkPermissionWebName", webName2)

        then:
        config1.getUnmarkPermissionWebName() == webName1
        config2.getUnmarkPermissionWebName() == webName2
        config1.getUnmarkPermissionWebName() != config2.getUnmarkPermissionWebName()
    }

    def "should handle property value changes"() {
        given:
        def initialWebName = "INITIAL_WEB_NAME"
        def updatedWebName = "UPDATED_WEB_NAME"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", initialWebName)
        def initialValue = unmarkRoleConfig.getUnmarkPermissionWebName()

        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", updatedWebName)
        def updatedValue = unmarkRoleConfig.getUnmarkPermissionWebName()

        then:
        initialValue == initialWebName
        updatedValue == updatedWebName
        initialValue != updatedValue
    }

    def "should verify toString method works (from Lombok @Getter)"() {
        given:
        def webName = "WEB_PERMISSION"
        def tabletName = "TABLET_PERMISSION"

        when:
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionWebName", webName)
        ReflectionTestUtils.setField(unmarkRoleConfig, "unmarkPermissionTabletName", tabletName)
        def toStringResult = unmarkRoleConfig.toString()

        then:
        toStringResult != null
        toStringResult instanceof String
        toStringResult.contains("UnmarkRoleConfig")
    }

    def "should verify equals and hashCode methods work"() {
        given:
        def config1 = new UnmarkRoleConfig()
        def config2 = new UnmarkRoleConfig()
        def webName = "SAME_WEB_NAME"
        def tabletName = "SAME_TABLET_NAME"

        when:
        ReflectionTestUtils.setField(config1, "unmarkPermissionWebName", webName)
        ReflectionTestUtils.setField(config1, "unmarkPermissionTabletName", tabletName)
        ReflectionTestUtils.setField(config2, "unmarkPermissionWebName", webName)
        ReflectionTestUtils.setField(config2, "unmarkPermissionTabletName", tabletName)

        then:
        config1.hashCode() != null
        config2.hashCode() != null
        // Note: Lombok @Getter doesn't generate equals/hashCode, so they use Object's implementation
        config1 != config2 // Different object instances
        config1.equals(config1) // Same object reference
    }
}
