package com.ttb.top.rbacservice.service.impl

import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException
import com.ttb.top.rbacservice.helper.MicrosoftApiHelper
import com.ttb.top.rbacservice.model.StaffInfo
import com.ttb.top.rbacservice.model.StaffListInquiryRequest
import com.ttb.top.rbacservice.model.entity.MovementStaffListEntity
import com.ttb.top.rbacservice.model.microsoft.GraphApiGroupDetailResponse
import com.ttb.top.rbacservice.model.microsoft.GraphApiMemberDetailResponse
import com.ttb.top.rbacservice.repository.MovementStaffListRepository
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository
import com.ttb.top.rbacservice.service.CacheService
import com.ttb.top.rbacservice.utils.ResourceReader
import org.instancio.Instancio
import spock.lang.Shared
import spock.lang.Specification

class StaffListServiceImplTest extends Specification {

    def resourceReader = new ResourceReader()
    def cacheService = Mock(CacheService)
    def microsoftApiHelper = Mock(MicrosoftApiHelper)
    def staffRolePermissionConfigRepository = Mock(StaffRolePermissionConfigRepository)
    def movementStaffListRepository = Mock(MovementStaffListRepository)

    def staffListServiceImpl = new StaffListServiceImpl(
            cacheService,
            microsoftApiHelper,
            staffRolePermissionConfigRepository,
            movementStaffListRepository
    )

    @Shared
    def movementStaffEntities = Instancio.ofList(MovementStaffListEntity).size(5).create()

    def "#1 when call inquiryStaffList should success then return data with staffList"() {
        given: "same staff data from movement db and cache"
        def request = StaffListInquiryRequest.builder()
                .branchCode("000")
                .permissionList(List.of("CREATE_RM_CREATE", "CREATE_RM_APPROVE", "CREATE_RM_REJECT"))
                .build()
        def permissionList = mockPermissionList()
        1 * staffRolePermissionConfigRepository.findByPermissionIdIn(_ as List<String>) >> permissionList
        1 * movementStaffListRepository.findAll() >> movementStaffEntities
        1 * cacheService.getMomentStaffList() >> movementStaffEntities
        permissionList.size() * cacheService.getAllStaffWithinRole(_ as String, _ as String) >> Instancio.ofList(StaffInfo).size(5).create()

        when:
        def actual = staffListServiceImpl.inquiryStaffList(request)

        then:
        noExceptionThrown()
        5 == actual.getStaffList().size()
    }

    def "#2 when call inquiryStaffList should success then return data with staffList"() {
        given: "staff data from movement db and cache not same or cache null"
        def request = StaffListInquiryRequest.builder()
                .branchCode("000")
                .permissionList(List.of("CREATE_RM_CREATE", "CREATE_RM_APPROVE", "CREATE_RM_REJECT"))
                .build()
        1 * staffRolePermissionConfigRepository.findByPermissionIdIn(_ as List<String>) >> mockPermissionList()
        def movementStaffEntitiesFromDb = List.of(
                MovementStaffListEntity.builder().employeeId("staff4").branchCode("100").build(),
                MovementStaffListEntity.builder().employeeId("staff5").branchCode("000").build(),
                MovementStaffListEntity.builder().employeeId("staff7").branchCode("000").build()
        )
        1 * movementStaffListRepository.findAll() >> movementStaffEntitiesFromDb
        1 * cacheService.getMomentStaffList() >> movementStaffEntitiesFromCache
        1 * cacheService.setMomentStaffList(movementStaffEntitiesFromDb)
        1 * microsoftApiHelper.getGroupsByRoleIds(_ as List) >> mockGroupList()
        1 * microsoftApiHelper.getMembersByGroupId(_ as List) >> Map.of(
                "ae79bd0e-a17b-4052-baf6-4eb0fed7f89b", mockMemberList(),
                "033c46c5-732c-4414-8321-c7fb9f0d06e5", mockMemberList()
        )
        def expectedIds = List.of("staff1","staff2","staff3","staff5","staff7")
        1 * cacheService.setAllStaffWithinRole(request.getBranchCode(), "TEP_BR_MAKER",
                { List<StaffInfo> staffInfoList ->
                    expectedIds.size() == staffInfoList.size()
                    staffInfoList.each {
                        expectedIds.contains(it.getEmployeeId())
                        request.getBranchCode() == it.getBranchCode()
                        "TEP_BR_MAKER" == it.getRoleId()
                    }
                })
        1 * cacheService.setAllStaffWithinRole(request.getBranchCode(), "TEP_BR_CHECKER",
                { List<StaffInfo> staffInfoList ->
                    expectedIds.size() == staffInfoList.size()
                    staffInfoList.each {
                        expectedIds.contains(it.getEmployeeId())
                        request.getBranchCode() == it.getBranchCode()
                        "TEP_BR_CHECKER" == it.getRoleId()
                    }
                })

        when:
        def actual = staffListServiceImpl.inquiryStaffList(request)

        then:
        noExceptionThrown()
        expectedIds == actual.getStaffList()

        where:
            movementStaffEntitiesFromCache << [movementStaffEntities, null]
    }

    def "#3 when call inquiryStaffList should success then return data with staffList"() {
        given: "movement staff data from db is empty but cache not empty"
        def request = StaffListInquiryRequest.builder()
                .branchCode("000")
                .permissionList(List.of("CREATE_RM_CREATE", "CREATE_RM_APPROVE", "CREATE_RM_REJECT"))
                .build()
        1 * staffRolePermissionConfigRepository.findByPermissionIdIn(_ as List<String>) >> mockPermissionList()
        1 * movementStaffListRepository.findAll() >> List.of()
        1 * cacheService.getMomentStaffList() >> movementStaffEntities
        1 * cacheService.setMomentStaffList(List.of())
        1 * microsoftApiHelper.getGroupsByRoleIds(_ as List) >> mockGroupList()
        1 * microsoftApiHelper.getMembersByGroupId(_ as List) >> Map.of(
                "ae79bd0e-a17b-4052-baf6-4eb0fed7f89b", mockMemberList(),
                "033c46c5-732c-4414-8321-c7fb9f0d06e5", mockMemberList()
        )
        def expectedIds = List.of("staff1","staff2","staff3","staff4")
        1 * cacheService.setAllStaffWithinRole(request.getBranchCode(), "TEP_BR_MAKER",
                { List<StaffInfo> staffInfoList ->
                    expectedIds.size() == staffInfoList.size()
                    staffInfoList.each {
                        expectedIds.contains(it.getEmployeeId())
                        request.getBranchCode() == it.getBranchCode()
                        "TEP_BR_MAKER" == it.getRoleId()
                    }
                })
        1 * cacheService.setAllStaffWithinRole(request.getBranchCode(), "TEP_BR_CHECKER",
                { List<StaffInfo> staffInfoList ->
                    expectedIds.size() == staffInfoList.size()
                    staffInfoList.each {
                        expectedIds.contains(it.getEmployeeId())
                        request.getBranchCode() == it.getBranchCode()
                        "TEP_BR_CHECKER" == it.getRoleId()
                    }
                })

        when:
        def actual = staffListServiceImpl.inquiryStaffList(request)

        then:
        noExceptionThrown()
        expectedIds == actual.getStaffList()
    }

    def "#4 when call inquiryStaffList should success then return data with staffList"() {
        given: "movement staff data from db is empty list and cache is null"
        def request = StaffListInquiryRequest.builder()
                .branchCode("000")
                .permissionList(List.of("CREATE_RM_CREATE", "CREATE_RM_APPROVE", "CREATE_RM_REJECT"))
                .build()
        1 * staffRolePermissionConfigRepository.findByPermissionIdIn(_ as List<String>) >> mockPermissionList()
        1 * movementStaffListRepository.findAll() >> List.of()
        1 * cacheService.getMomentStaffList() >> null
        1 * cacheService.setMomentStaffList(List.of())
        1 * microsoftApiHelper.getGroupsByRoleIds(_ as List) >> mockGroupList()
        1 * microsoftApiHelper.getMembersByGroupId(_ as List) >> Map.of(
                "ae79bd0e-a17b-4052-baf6-4eb0fed7f89b", mockMemberList(),
                "033c46c5-732c-4414-8321-c7fb9f0d06e5", mockMemberList()
        )
        def expectedIds = List.of("staff1","staff2","staff3","staff4")
        1 * cacheService.setAllStaffWithinRole(request.getBranchCode(), "TEP_BR_MAKER",
                { List<StaffInfo> staffInfoList ->
                    expectedIds.size() == staffInfoList.size()
                    staffInfoList.each {
                        expectedIds.contains(it.getEmployeeId())
                        request.getBranchCode() == it.getBranchCode()
                        "TEP_BR_MAKER" == it.getRoleId()
                    }
                })
        1 * cacheService.setAllStaffWithinRole(request.getBranchCode(), "TEP_BR_CHECKER",
                { List<StaffInfo> staffInfoList ->
                    expectedIds.size() == staffInfoList.size()
                    staffInfoList.each {
                        expectedIds.contains(it.getEmployeeId())
                        request.getBranchCode() == it.getBranchCode()
                        "TEP_BR_CHECKER" == it.getRoleId()
                    }
                })

        when:
        def actual = staffListServiceImpl.inquiryStaffList(request)

        then:
        noExceptionThrown()
        expectedIds == actual.getStaffList()
    }

    def "#5 when call inquiryStaffList should success then return data with staffList"() {
        given: "movement staff data from db and cache is all empty list"
        def request = StaffListInquiryRequest.builder()
                .branchCode("000")
                .permissionList(List.of("CREATE_RM_CREATE", "CREATE_RM_APPROVE", "CREATE_RM_REJECT"))
                .build()
        def permissionList = mockPermissionList()
        1 * staffRolePermissionConfigRepository.findByPermissionIdIn(_ as List<String>) >> permissionList
        1 * movementStaffListRepository.findAll() >> List.of()
        1 * cacheService.getMomentStaffList() >> List.of()
        permissionList.size() * cacheService.getAllStaffWithinRole(_ as String, _ as String) >> Instancio.ofList(StaffInfo).size(5).create()

        when:
        def actual = staffListServiceImpl.inquiryStaffList(request)

        then:
        noExceptionThrown()
        5 == actual.getStaffList().size()
    }

    def "when call inquiryStaffList then got error from getRolesByPermissions then throw DatabaseErrorException"() {
        given:
        def request = StaffListInquiryRequest.builder()
                .branchCode("000")
                .permissionList(List.of("CREATE_RM_CREATE", "CREATE_RM_APPROVE", "CREATE_RM_REJECT"))
                .build()
        1 * staffRolePermissionConfigRepository.findByPermissionIdIn(_ as List<String>) >> {
            throw new RuntimeException("Error DB") }

        when:
        staffListServiceImpl.inquiryStaffList(request)

        then:
        thrown(DatabaseErrorException)
    }


    def mockPermissionList() {
        return resourceReader.readValue("permission/MockPermissionsForRm.json", List.class)
    }

    def mockGroupList() {
        return List.of(GraphApiGroupDetailResponse.builder().id("ae79bd0e-a17b-4052-baf6-4eb0fed7f89b").displayName("TEP_BR_MAKER").build(),
                GraphApiGroupDetailResponse.builder().id("033c46c5-732c-4414-8321-c7fb9f0d06e5").displayName("TEP_BR_CHECKER").build())
    }

    def mockMemberList() {
        return List.of(
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff1")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 000")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff2")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 000")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff3")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 000")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff4")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 000")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff5")
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff6")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 005")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff7")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 3212")
                                .build())
                        .build())

    }

    def "#6 when call inquiryStaffList with branch 001 should success then return data with staffList"() {
        given: "staff data from movement db and cache not same or cache null"
        def request = StaffListInquiryRequest.builder()
                .branchCode("001")
                .permissionList(List.of("CREATE_RM_CREATE", "CREATE_RM_APPROVE", "CREATE_RM_REJECT"))
                .build()
        1 * staffRolePermissionConfigRepository.findByPermissionIdIn(_ as List<String>) >> mockPermissionList()
        def movementStaffEntitiesFromDb = List.of(
                MovementStaffListEntity.builder().employeeId("staff1").branchCode("001").build()
        )
        1 * movementStaffListRepository.findAll() >> movementStaffEntitiesFromDb
        1 * cacheService.getMomentStaffList() >> movementStaffEntitiesFromCache
        1 * cacheService.setMomentStaffList(movementStaffEntitiesFromDb)
        1 * microsoftApiHelper.getGroupsByRoleIds(_ as List) >> mockGroupList()
        1 * microsoftApiHelper.getMembersByGroupId(_ as List) >> Map.of(
                "ae79bd0e-a17b-4052-baf6-4eb0fed7f89b", mockMemberListCase001(),
                "033c46c5-732c-4414-8321-c7fb9f0d06e5", mockMemberListCase001()
        )
        def expectedIds = List.of("staff1")
        1 * cacheService.setAllStaffWithinRole(request.getBranchCode(), "TEP_BR_MAKER",
                { List<StaffInfo> staffInfoList ->
                    expectedIds.size() == staffInfoList.size()
                    staffInfoList.each {
                        expectedIds.contains(it.getEmployeeId())
                        request.getBranchCode() == it.getBranchCode()
                        "TEP_BR_MAKER" == it.getRoleId()
                    }
                })
        1 * cacheService.setAllStaffWithinRole(request.getBranchCode(), "TEP_BR_CHECKER",
                { List<StaffInfo> staffInfoList ->
                    expectedIds.size() == staffInfoList.size()
                    staffInfoList.each {
                        expectedIds.contains(it.getEmployeeId())
                        request.getBranchCode() == it.getBranchCode()
                        "TEP_BR_CHECKER" == it.getRoleId()
                    }
                })

        when:
        def actual = staffListServiceImpl.inquiryStaffList(request)

        then:
        noExceptionThrown()
        expectedIds == actual.getStaffList()

        where:
        movementStaffEntitiesFromCache << [movementStaffEntities, null]
    }

    def mockMemberListCase001() {
        return List.of(
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff1")
                        .msExchExtensionAttribute27("**********")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 007")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff2")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 000")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff4")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 000")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff5")
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff6")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 005")
                                .build())
                        .build(),
                GraphApiMemberDetailResponse.builder()
                        .onPremisesSamAccountName("staff7")
                        .onPremisesExtensionAttributes(GraphApiMemberDetailResponse.OnPremisesExtensionAttributes.builder()
                                .extensionAttribute10("สำนักงานใหญ่ | 3212")
                                .build())
                        .build())

    }
}
