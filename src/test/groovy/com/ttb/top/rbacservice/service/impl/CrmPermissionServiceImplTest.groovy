package com.ttb.top.rbacservice.service.impl

import com.ttb.top.library.crmapihelper.model.CrmResponseModel
import com.ttb.top.library.exceptionmodel.exception.BadRequestException
import com.ttb.top.library.exceptionmodel.exception.CommonException
import com.ttb.top.library.exceptionmodel.exception.GenericException
import com.ttb.top.library.exceptionmodel.exception.LegacySystemFailureException
import com.ttb.top.rbacservice.feign.CrmsspServiceClient
import com.ttb.top.rbacservice.model.StaffLoginInfo
import com.ttb.top.rbacservice.model.feign.crmssp.UserRetrieveUserInfoRequest
import com.ttb.top.rbacservice.model.feign.crmssp.UserRetrieveUserInfoResponse
import com.ttb.top.rbacservice.service.CacheService
import com.ttb.top.rbacservice.utils.ResourceReader
import feign.FeignException
import feign.Request
import feign.RequestTemplate
import io.github.resilience4j.circuitbreaker.CallNotPermittedException
import org.apache.commons.lang3.StringUtils
import org.instancio.Instancio
import org.springframework.http.HttpHeaders
import spock.lang.Specification

class CrmPermissionServiceImplTest extends Specification {

    def resourceReader = new ResourceReader()
    def httpHeaders = Mock(HttpHeaders)
    def cacheService = Mock(CacheService)
    def crmsspServiceClient = Mock(CrmsspServiceClient)

    def crmPermissionServiceImpl = new CrmPermissionServiceImpl(
            httpHeaders,
            cacheService,
            crmsspServiceClient
    )

    def "when call inquiry Staff Login Info w/o staffId should fail then throw BadRequestException"() {
        given: "get cache staff login info and have data"

        when:
        def response = crmPermissionServiceImpl.getPermission()

        then:
        thrown(BadRequestException);
    }

    def "when call inquiry Staff Login Info should success then return data with staff info"() {
        given: "get cache crm staff role permission and have data"
        httpHeaders.getFirst(_ as String) >> "01234"

        cacheService.getCRMStaffRolePermissionWithStaffId(_ as String) >> mockCrmPermission()

        when:
        def response = crmPermissionServiceImpl.getPermission()

        then:
        noExceptionThrown()
    }

    def "when call inquiry Staff Login Info should success then return data with staff info"() {
        given: "get cache crm staff role permission and not have data"
        httpHeaders.getFirst(_ as String) >> "01234"

        cacheService.getCRMStaffRolePermissionWithStaffId(_ as String) >> null
        crmsspServiceClient.userRetrieveUserInfo(_ as UserRetrieveUserInfoRequest)
                >> CrmResponseModel.success(mockUserRetrieveUserInfo())
        cacheService.setCRMStaffRolePermissionWithStaffId(_ as String, _ as StaffLoginInfo)
                >> mockCrmPermission()

        when:
        def response = crmPermissionServiceImpl.getPermission()

        then:
        noExceptionThrown()
    }

    def "when call inquiry Staff Login Info should fail then throw LegacySystemFailureException"() {
        given: "get cache staff login info and not have data"
        httpHeaders.getFirst(_ as String) >> "01234"

        cacheService.getStaffLoginInfoWithStaffId(_ as String) >> null
        crmsspServiceClient.userRetrieveUserInfo(_ as UserRetrieveUserInfoRequest)
                >> { throw mockFeignExceptionErrorWithBodyJson(
                Request.HttpMethod.POST, 401, null, mockErrorEmployeeIdNotFoundCrmResponse()) }

        when:
        def response = crmPermissionServiceImpl.getPermission()

        then:
        thrown(LegacySystemFailureException)
    }

    def "when call inquiry Staff Login Info should fail then throw pass through error"() {
        given: "get cache staff login info and not have data"
        httpHeaders.getFirst(_ as String) >> "01234"

        cacheService.getStaffLoginInfoWithStaffId(_ as String) >> null
        crmsspServiceClient.userRetrieveUserInfo(_ as UserRetrieveUserInfoRequest)
                >> { throw mockFeignExceptionErrorWithBodyJson(
                Request.HttpMethod.POST, 404, null, mockErrorEmployeeIdNotFoundCrmResponse()) }

        when:
        crmPermissionServiceImpl.getPermission()

        then:
        def error = thrown(CommonException)
        error.errorCode == "8100"
    }

    def "when call inquiry Staff Login Info should fail then throw CallNotPermittedException"() {
        given: "get cache staff login info and not have data"
        httpHeaders.getFirst(_ as String) >> "01234"

        cacheService.getStaffLoginInfoWithStaffId(_ as String) >> null
        crmsspServiceClient.userRetrieveUserInfo(_ as UserRetrieveUserInfoRequest)
                >> { throw Instancio.create(CallNotPermittedException) }

        when:
        def response = crmPermissionServiceImpl.getPermission()

        then:
        thrown(GenericException)
    }

    def mockCrmPermission() {
        return resourceReader.readValue("crmpermission/MockCrmPermissions.json", StaffLoginInfo.class)
    }

    def mockUserRetrieveUserInfo() {
        return resourceReader.readValue("crmpermission/MockCrmPermissions.json", UserRetrieveUserInfoResponse.class)
    }

    String mockErrorEmployeeIdNotFoundCrmResponse() {
        return """{"data":null,"statusCode":"2","statusDescription":"EmployeeId not found"}""";
    }

    FeignException mockFeignExceptionErrorWithBodyJson(Request.HttpMethod method, int status,
                                                       String bodyReq, String bodyRes) {
        Map<String, Collection<String>> headers = new HashMap<String, Collection<String>>();
        Request.Body bodyReqToBodyType = StringUtils.isEmpty(bodyReq) ? null : Request.Body.create(bodyReq);
        Request request = Request.create(method, "url", headers, bodyReqToBodyType, new RequestTemplate());
        return new FeignException.FeignClientException(status, "", request, bodyRes.getBytes(), headers);
    }
}
