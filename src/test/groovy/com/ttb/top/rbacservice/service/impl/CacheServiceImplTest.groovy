package com.ttb.top.rbacservice.service.impl

import com.ttb.top.rbacservice.model.StaffInfo
import com.ttb.top.rbacservice.model.entity.MovementStaffListEntity
import org.instancio.Instancio
import spock.lang.Specification

class CacheServiceImplTest extends Specification {

    def cacheServiceImpl = new CacheServiceImpl(
    )

    def "getAllStaffWithinRole default"() {
        when:
        def actual = cacheServiceImpl.getAllStaffWithinRole("branchCode", "roleId")

        then:
        noExceptionThrown()
        0 == actual.size()
    }

    def "setAllStaffWithinRole"() {
        given:
        List<StaffInfo> staffInfoList = Instancio.ofList(StaffInfo.class).size(5).create()
        when:
        def actual = cacheServiceImpl.setAllStaffWithinRole("branchCode", "roleId", staffInfoList)

        then:
        noExceptionThrown()
        staffInfoList == actual
    }

    def "getMomentStaffList default"() {
        when:
        def actual = cacheServiceImpl.getMomentStaffList()

        then:
        noExceptionThrown()
        null == actual
    }

    def "setMomentStaffList"() {
        given:
        List<MovementStaffListEntity> movementStaffListEntities = Instancio.ofList(MovementStaffListEntity.class)
                .size(5).create()
        when:
        def actual = cacheServiceImpl.setMomentStaffList(movementStaffListEntities)

        then:
        noExceptionThrown()
        movementStaffListEntities == actual
    }
}
