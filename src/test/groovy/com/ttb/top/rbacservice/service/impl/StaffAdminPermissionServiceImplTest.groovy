package com.ttb.top.rbacservice.service.impl.service

import com.ttb.top.rbacservice.feign.AdminServiceClient
import com.ttb.top.rbacservice.model.PermissionInquiryRequest
import com.ttb.top.rbacservice.model.V1PermissionResponse
import com.ttb.top.rbacservice.model.feign.StaffAdminInquiryRequest
import com.ttb.top.rbacservice.model.feign.StaffAdminInquiryResponse
import com.ttb.top.rbacservice.service.StaffRolePermissionConfigCacheService
import com.ttb.top.rbacservice.service.impl.StaffAdminPermissionServiceImpl
import com.ttb.top.library.commonmodel.model.ResponseModel
import org.springframework.http.HttpHeaders
import spock.lang.Specification
import com.ttb.top.library.exceptionmodel.exception.BadRequestException

class StaffAdminPermissionServiceImplTest extends Specification {

    def httpHeaders = Mock(HttpHeaders)
    def adminServiceClient = Mock(AdminServiceClient)
    def staffRolePermissionConfigCacheService = Mock(StaffRolePermissionConfigCacheService)
    def staffAdminPermissionService = new StaffAdminPermissionServiceImpl(httpHeaders, adminServiceClient, staffRolePermissionConfigCacheService)


    def "when call getPermission should return success and invoke service"() {
        given:
        def permissionInquiryRequest = PermissionInquiryRequest.builder()
                .roleId("Test").staffId("1111").build();
        def permissionList = new V1PermissionResponse(List.of("role1", "role2"))
        staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId("Test") >> permissionList

        when:
        def result = staffAdminPermissionService.getPermission(permissionInquiryRequest)

        then:
        result.getPermissionList().size() == 2
        result.getPermissionList().get(0) == "role1"
        result.getPermissionList().get(1) == "role2"
    }

    def "when call getPermission without roleId should return success and invoke service"() {
        given:
        def permissionInquiryRequest = PermissionInquiryRequest.builder()
                .staffId("1111").build();
        def permissionList = new V1PermissionResponse(List.of("role1", "role2"))
        def staffAdminInquiryResponse = StaffAdminInquiryResponse.builder().cnRole("Test").build();
        def responseAdminServiceClient = ResponseModel.success(staffAdminInquiryResponse)

        staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId("Test") >> permissionList
        adminServiceClient.staffAdminInfoInquiry(httpHeaders, _ as StaffAdminInquiryRequest) >> responseAdminServiceClient

        when:
        def result = staffAdminPermissionService.getPermission(permissionInquiryRequest)

        then:
        result.getPermissionList().size() == 2
        result.getPermissionList().get(0) == "role1"
        result.getPermissionList().get(1) == "role2"
    }

    def "when call getPermission and no staffId will throw BadException"() {
        given:
        def permissionInquiryRequest = PermissionInquiryRequest.builder().build();

        when:
        def result = staffAdminPermissionService.getPermission(permissionInquiryRequest)

        then:
        def e = thrown(BadRequestException)
        e.errorCode == "8000"
    }

}
