package com.ttb.top.rbacservice.service.impl

import com.ttb.top.library.exceptionmodel.exception.BadRequestException
import com.ttb.top.rbacservice.configuration.UnmarkRoleConfig
import com.ttb.top.rbacservice.model.UnmarkRoleResponse
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository
import org.springframework.http.HttpHeaders
import spock.lang.Specification
import spock.lang.Unroll

class UnmarkRoleServiceImplCompleteTest extends Specification {

    def httpHeaders = Mock(HttpHeaders)
    def unmarkRoleConfig = Mock(UnmarkRoleConfig)
    def staffRolePermissionConfigRepository = Mock(StaffRolePermissionConfigRepository)

    def unmarkRoleService = new UnmarkRoleServiceImpl(
            httpHeaders,
            unmarkRoleConfig,
            staffRolePermissionConfigRepository
    )

    // ========== VALIDATION TESTS ==========

    def "should throw BadRequestException when STAFF-ID header is null"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> null
        httpHeaders.getFirst("channel") >> "TEP_WEB"

        when:
        unmarkRoleService.getUnmaskRoles()

        then:
        thrown(BadRequestException)
    }

    def "should throw BadRequestException when channel header is null"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> "12345"
        httpHeaders.getFirst("channel") >> null

        when:
        unmarkRoleService.getUnmaskRoles()

        then:
        thrown(BadRequestException)
    }

    def "should throw BadRequestException when both headers are null"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> null
        httpHeaders.getFirst("channel") >> null

        when:
        unmarkRoleService.getUnmaskRoles()

        then:
        thrown(BadRequestException)
    }

    def "validateRequestField should not throw exception when both headers are present"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> "12345"
        httpHeaders.getFirst("channel") >> "TEP_WEB"

        when:
        unmarkRoleService.validateRequestField()

        then:
        noExceptionThrown()
    }

    def "validateRequestField should not throw exception when headers are empty strings"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> ""
        httpHeaders.getFirst("channel") >> ""

        when:
        unmarkRoleService.validateRequestField()

        then:
        noExceptionThrown()
    }

    // ========== TEP_WEB CHANNEL TESTS ==========

    def "should return web roles when channel is TEP_WEB with multiple roles"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def expectedRoles = ["ROLE1", "ROLE2", "ROLE3"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result instanceof UnmarkRoleResponse
        result.roles == expectedRoles
        result.roles.size() == 3
//        1 * unmarkRoleConfig.getUnmarkPermissionWebName()
//        1 * staffRolePermissionConfigRepository.findByPermissionId(webPermissionName)
        0 * unmarkRoleConfig.getUnmarkPermissionTabletName()
    }

    def "should return single web role when channel is TEP_WEB"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def expectedRoles = ["SINGLE_WEB_ROLE"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        result.roles.size() == 1
        result.roles[0] == "SINGLE_WEB_ROLE"
    }

    def "should return empty list when no web roles found for TEP_WEB channel"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "NON_EXISTENT_WEB_PERMISSION"
        def emptyRoles = []

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> emptyRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == []
        result.roles.isEmpty()
        result.roles.size() == 0
//        1 * unmarkRoleConfig.getUnmarkPermissionWebName()
//        1 * staffRolePermissionConfigRepository.findByPermissionId(webPermissionName)
    }

    // ========== NON-TEP_WEB CHANNEL TESTS ==========

    def "should return tablet roles when channel is not TEP_WEB"() {
        given:
        def staffId = "12345"
        def channel = "TEP_TABLET"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE1", "TABLET_ROLE2"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result instanceof UnmarkRoleResponse
        result.roles == expectedRoles
        result.roles.size() == 2
    //    1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
    //    1 * staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName)
        0 * unmarkRoleConfig.getUnmarkPermissionWebName()
    }

    def "should return single tablet role when channel is not TEP_WEB"() {
        given:
        def staffId = "12345"
        def channel = "MOBILE_APP"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["SINGLE_TABLET_ROLE"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        result.roles.size() == 1
        result.roles[0] == "SINGLE_TABLET_ROLE"
    }

    def "should return empty list when no tablet roles found for non-TEP_WEB channel"() {
        given:
        def staffId = "12345"
        def channel = "OTHER_CHANNEL"
        def tabletPermissionName = "NON_EXISTENT_TABLET_PERMISSION"
        def emptyRoles = []

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> emptyRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == []
        result.roles.isEmpty()
        result.roles.size() == 0
//        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
//        1 * staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName)
    }

    // ========== CHANNEL EDGE CASES ==========

    @Unroll
    def "should use tablet logic for channel: '#channel'"() {
        given:
        def staffId = "12345"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
//        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        0 * unmarkRoleConfig.getUnmarkPermissionWebName()

        where:
        channel << ["tep_web", "TEP_TABLET", "MOBILE", "", "   ", "null", "undefined"]
    }

    def "should handle case sensitivity - lowercase tep_web uses tablet logic"() {
        given:
        def staffId = "12345"
        def channel = "tep_web" // lowercase
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE1"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
//        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        0 * unmarkRoleConfig.getUnmarkPermissionWebName()
    }

    def "should handle whitespace in channel - uses tablet logic"() {
        given:
        def staffId = "12345"
        def channel = " TEP_WEB " // with whitespace
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE1"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
//        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        0 * unmarkRoleConfig.getUnmarkPermissionWebName()
    }

    // ========== MULTIPLE CALLS TEST ==========

    def "should handle multiple calls with same parameters consistently"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def expectedRoles = ["ROLE1", "ROLE2"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> expectedRoles

        when:
        def result1 = unmarkRoleService.getUnmaskRoles()
        def result2 = unmarkRoleService.getUnmaskRoles()

        then:
        result1 != null
        result2 != null
        result1.roles == expectedRoles
        result2.roles == expectedRoles
        result1.roles == result2.roles
//        2 * unmarkRoleConfig.getUnmarkPermissionWebName()
//        2 * staffRolePermissionConfigRepository.findByPermissionId(webPermissionName)
    }

    // ========== RESPONSE OBJECT VERIFICATION ==========

    def "should return proper UnmarkRoleResponse object structure"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def roles = ["ROLE1"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> roles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result instanceof UnmarkRoleResponse
        result.roles != null
        result.roles instanceof List
        result.roles == roles
    }
}
