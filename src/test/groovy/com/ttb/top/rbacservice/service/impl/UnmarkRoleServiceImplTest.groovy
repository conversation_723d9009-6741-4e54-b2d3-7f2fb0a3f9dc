package com.ttb.top.rbacservice.service.impl

import spock.lang.Specification
import com.ttb.top.rbacservice.configuration.UnmarkRoleConfig
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository
import org.springframework.http.HttpHeaders

class UnmarkRoleServiceImplTest extends Specification{

    def httpHeaders = Mock(HttpHeaders)
    def unmarkRoleConfig = Mock(UnmarkRoleConfig)
    def staffRolePermissionConfigRepository = Mock(StaffRolePermissionConfigRepository)

    def unmarkRoleService = new UnmarkRoleServiceImpl(
            httpHeaders,
            unmarkRoleConfig,
            staffRolePermissionConfigRepository
    )

    def "when call getUnmaskRoles and no staffId will throw BadException"() {
        given:
        httpHeaders.getFirst(_ as String) >> null

        when:
        unmarkRoleService.getUnmaskRoles()

        then:
        def e = thrown(BadRequestException)
        e.errorCode == "8000"
    }
}
