package com.ttb.top.rbacservice.service.impl

import com.ttb.top.library.exceptionmodel.exception.BadRequestException
import com.ttb.top.rbacservice.configuration.UnmarkRoleConfig
import com.ttb.top.rbacservice.model.UnmarkRoleResponse
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository
import org.springframework.http.HttpHeaders
import spock.lang.Specification

class UnmarkRoleServiceImplTest extends Specification {

    def httpHeaders = Mock(HttpHeaders)
    def unmarkRoleConfig = Mock(UnmarkRoleConfig)
    def staffRolePermissionConfigRepository = Mock(StaffRolePermissionConfigRepository)

    def unmarkRoleService = new UnmarkRoleServiceImpl(
            httpHeaders,
            unmarkRoleConfig,
            staffRolePermissionConfigRepository
    )

    def "should throw BadRequestException when STAFF-ID header is null"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> null
        httpHeaders.getFirst("channel") >> "TEP_WEB"

        when:
        unmarkRoleService.getUnmaskRoles()

        then:
        thrown(BadRequestException)
    }

    def "should throw BadRequestException when channel header is null"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> "12345"
        httpHeaders.getFirst("channel") >> null

        when:
        unmarkRoleService.getUnmaskRoles()

        then:
        thrown(BadRequestException)
    }

    def "should throw BadRequestException when both STAFF-ID and channel headers are null"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> null
        httpHeaders.getFirst("channel") >> null

        when:
        unmarkRoleService.getUnmaskRoles()

        then:
        thrown(BadRequestException)
    }

    def "should return web roles when channel is TEP_WEB"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def expectedRoles = ["ROLE1", "ROLE2", "ROLE3"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        1 * unmarkRoleConfig.getUnmarkPermissionWebName()
        1 * staffRolePermissionConfigRepository.findByPermissionId(webPermissionName)
        0 * unmarkRoleConfig.getUnmarkPermissionTabletName()
    }

    def "should return tablet roles when channel is not TEP_WEB"() {
        given:
        def staffId = "12345"
        def channel = "TEP_TABLET"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE1", "TABLET_ROLE2"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        1 * staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName)
        0 * unmarkRoleConfig.getUnmarkPermissionWebName()
    }

    def "should return tablet roles when channel is empty string"() {
        given:
        def staffId = "12345"
        def channel = ""
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE1"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        1 * staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName)
    }

    def "should return tablet roles when channel is different value"() {
        given:
        def staffId = "12345"
        def channel = "MOBILE_APP"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["MOBILE_ROLE1", "MOBILE_ROLE2"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        1 * staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName)
    }

    def "should return empty roles list when repository returns empty list for web channel"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def expectedRoles = []

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        result.roles.isEmpty()
    }

    def "should return empty roles list when repository returns empty list for tablet channel"() {
        given:
        def staffId = "12345"
        def channel = "TEP_TABLET"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = []

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        result.roles.isEmpty()
    }

    def "should handle case sensitivity for TEP_WEB channel correctly"() {
        given:
        def staffId = "12345"
        def channel = "tep_web" // lowercase
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE1"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        // Should use tablet permission since case doesn't match exactly
        result != null
        result.roles == expectedRoles
        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        0 * unmarkRoleConfig.getUnmarkPermissionWebName()
    }

    def "validateRequestField should not throw exception when both headers are present"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> "12345"
        httpHeaders.getFirst("channel") >> "TEP_WEB"

        when:
        unmarkRoleService.validateRequestField()

        then:
        noExceptionThrown()
    }

    def "validateRequestField should throw BadRequestException when STAFF-ID is null"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> null
        httpHeaders.getFirst("channel") >> "TEP_WEB"

        when:
        unmarkRoleService.validateRequestField()

        then:
        thrown(BadRequestException)
    }

    def "validateRequestField should throw BadRequestException when channel is null"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> "12345"
        httpHeaders.getFirst("channel") >> null

        when:
        unmarkRoleService.validateRequestField()

        then:
        thrown(BadRequestException)
    }

    def "should return null roles when repository returns null for web channel"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> null

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == null
        1 * unmarkRoleConfig.getUnmarkPermissionWebName()
        1 * staffRolePermissionConfigRepository.findByPermissionId(webPermissionName)
    }

    def "should return null roles when repository returns null for tablet channel"() {
        given:
        def staffId = "12345"
        def channel = "TEP_TABLET"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> null

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == null
        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        1 * staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName)
    }

    def "should handle whitespace in channel header for TEP_WEB"() {
        given:
        def staffId = "12345"
        def channel = " TEP_WEB " // with whitespace
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE1"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        // Should use tablet permission since whitespace doesn't match exactly
        result != null
        result.roles == expectedRoles
        1 * unmarkRoleConfig.getUnmarkPermissionTabletName()
        0 * unmarkRoleConfig.getUnmarkPermissionWebName()
    }

    def "should handle single role in list for web channel"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def expectedRoles = ["SINGLE_WEB_ROLE"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        result.roles.size() == 1
        result.roles[0] == "SINGLE_WEB_ROLE"
    }

    def "should handle single role in list for tablet channel"() {
        given:
        def staffId = "12345"
        def channel = "OTHER_CHANNEL"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["SINGLE_TABLET_ROLE"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result != null
        result.roles == expectedRoles
        result.roles.size() == 1
        result.roles[0] == "SINGLE_TABLET_ROLE"
    }

    def "should verify UnmarkRoleResponse builder is used correctly for web channel"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def expectedRoles = ["ROLE1", "ROLE2"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result instanceof UnmarkRoleResponse
        result.roles == expectedRoles
    }

    def "should verify UnmarkRoleResponse builder is used correctly for tablet channel"() {
        given:
        def staffId = "12345"
        def channel = "TABLET"
        def tabletPermissionName = "TABLET_UNMASK_PERMISSION"
        def expectedRoles = ["TABLET_ROLE1", "TABLET_ROLE2"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionTabletName() >> tabletPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(tabletPermissionName) >> expectedRoles

        when:
        def result = unmarkRoleService.getUnmaskRoles()

        then:
        result instanceof UnmarkRoleResponse
        result.roles == expectedRoles
    }

    def "validateRequestField should throw BadRequestException when both headers are empty strings"() {
        given:
        httpHeaders.getFirst("STAFF-ID") >> ""
        httpHeaders.getFirst("channel") >> ""

        when:
        unmarkRoleService.validateRequestField()

        then:
        // Empty strings are not null, so validation should pass
        noExceptionThrown()
    }

    def "should handle multiple calls to getUnmaskRoles with same parameters"() {
        given:
        def staffId = "12345"
        def channel = "TEP_WEB"
        def webPermissionName = "WEB_UNMASK_PERMISSION"
        def expectedRoles = ["ROLE1", "ROLE2"]

        httpHeaders.getFirst("STAFF-ID") >> staffId
        httpHeaders.getFirst("channel") >> channel
        unmarkRoleConfig.getUnmarkPermissionWebName() >> webPermissionName
        staffRolePermissionConfigRepository.findByPermissionId(webPermissionName) >> expectedRoles

        when:
        def result1 = unmarkRoleService.getUnmaskRoles()
        def result2 = unmarkRoleService.getUnmaskRoles()

        then:
        result1 != null
        result2 != null
        result1.roles == expectedRoles
        result2.roles == expectedRoles
        result1.roles == result2.roles
        // Verify methods are called for each invocation
        2 * unmarkRoleConfig.getUnmarkPermissionWebName()
        2 * staffRolePermissionConfigRepository.findByPermissionId(webPermissionName)
    }
}
