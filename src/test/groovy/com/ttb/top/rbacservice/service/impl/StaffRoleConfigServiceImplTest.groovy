package com.ttb.top.rbacservice.service.impl

import com.ttb.top.rbacservice.mapper.StaffRoleConfigMapper
import com.ttb.top.rbacservice.model.entity.StaffRoleConfigEntity
import com.ttb.top.rbacservice.service.StaffRoleConfigCacheService

import org.mapstruct.factory.Mappers
import spock.lang.Specification

class StaffRoleConfigServiceImplTest extends Specification {

    def staffRoleConfigCacheService = Mock(StaffRoleConfigCacheService)
    def staffRoleConfigMapper = Spy(Mappers.getMapper(StaffRoleConfigMapper.class))
    def staffRoleConfigService = new StaffRoleConfigServiceImpl(staffRoleConfigMapper, staffRoleConfigCacheService)

    def "when call inquiryStaffRoleConfig and data found should not return null"() {
        given:
        1 * staffRoleConfigCacheService.inquiryStaffRoleConfig() >> mockStaffRoleConfigEntity()

        when:
        def response = staffRoleConfigService.inquiryStaffRoleConfig()

        then:
        noExceptionThrown()
        response.getStaffRole().size() == 2
    }

    def mockStaffRoleConfigEntity() {
        def staffRole1 = StaffRoleConfigEntity.builder()
                .roleId("TEP_RM_SUP1")
                .roleDescription("roleDescription")
                .roleType("NON_BRANCH")
                .displayRoleType("OTHER")
                .build()

        def staffRole2 = StaffRoleConfigEntity.builder()
                .roleId("TEP_AL_POWER_USER")
                .roleDescription("roleDescription")
                .roleType("NON_BRANCH")
                .displayRoleType("AL SALE")
                .build()

        return List.of(staffRole1, staffRole2)
    }

}
