package com.ttb.top.rbacservice.service.impl

import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum
import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException
import com.ttb.top.library.exceptionmodel.exception.GenericException
import com.ttb.top.rbacservice.model.entity.StaffRoleConfigEntity
import com.ttb.top.rbacservice.repository.StaffRoleConfigRepository

import spock.lang.Specification

class StaffRoleConfigCacheServiceImplTest extends Specification {

    def staffRoleConfigRepository = Mock(StaffRoleConfigRepository)
    def staffRoleConfigCacheService = new StaffRoleConfigCacheServiceImpl(staffRoleConfigRepository)

    def "when call inquiryStaffRoleConfig and data found should not return null"() {
        given:
        1 * staffRoleConfigRepository.findAll() >> mockStaffRoleConfigEntity()

        when:
        def response = staffRoleConfigCacheService.inquiryStaffRoleConfig()

        then:
        noExceptionThrown()
        response.size() == 2
    }

    def "when call inquiryStaffRoleConfig and data not found should throw GenericException"() {
        given:
        1 * staffRoleConfigRepository.findAll() >> List.of()

        when:
        staffRoleConfigCacheService.inquiryStaffRoleConfig()

        then:
        GenericException ex = thrown GenericException
        ex.getErrorCode() == ResponseCodeEnum.DATA_NOT_FOUND.getCode()
    }

    def "when call inquiryStaffRoleConfig and database error should throw DatabaseErrorException"() {
        given:
        1 * staffRoleConfigRepository.findAll() >> { throw new Exception() }

        when:
        staffRoleConfigCacheService.inquiryStaffRoleConfig()

        then:
        thrown DatabaseErrorException
    }

    def mockStaffRoleConfigEntity() {
        def staffRole1 = StaffRoleConfigEntity.builder()
                .roleId("TOP_RM_SUP1")
                .roleDescription("roleDescription")
                .roleType("NON_BRANCH")
                .build()

        def staffRole2 = StaffRoleConfigEntity.builder()
                .roleId("TOP_RM_SUP1")
                .roleDescription("roleDescription")
                .roleType("NON_BRANCH")
                .build()

        return List.of(staffRole1, staffRole2)
    }

}
