package com.ttb.top.rbacservice.service;

import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.rbacservice.feign.AuthServiceClient;
import com.ttb.top.rbacservice.model.V1PermissionResponse;
import com.ttb.top.rbacservice.model.feign.StaffLoginInfoInquiry;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.util.Arrays;
import java.util.List;

import static com.ttb.top.rbacservice.constant.RbacServiceConstant.STAFF_ID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
public class PermissionServiceTest {
    @Mock
    HttpHeaders httpHeaders;

    @InjectMocks
    PermissionService permissionService;

    @Mock
    AuthServiceClient authServiceClient;

    @Mock
    StaffRolePermissionConfigCacheService staffRolePermissionConfigCacheService;


    @Test
    void getPermissionSuccessShouldReturnPermissionList() {
        Mockito.when(httpHeaders.getFirst(STAFF_ID)).thenReturn("testStaffId");

        StaffLoginInfoInquiry staffLoginInfoInquiry = new StaffLoginInfoInquiry();
        staffLoginInfoInquiry.setCnRole("TOP_BR_MAKER");

        Mockito.when(authServiceClient.staffLoginInfoInquiry(any(HttpHeaders.class))).thenReturn(ResponseModel.success(staffLoginInfoInquiry));

        List<String> permissionList = Arrays.asList("CREATE_RM_VIEW", "CREATE_RM_RECALL", "CREATE_RM_CREATE",
                "CREATE_RM_UPDATE", "CREATE_RM_CANCEL", "NEW_CAR_VIEW", "NEW_CAR_CREATE", "NEW_CAR_UPDATE",
                "NEW_CAR_CANCEL", "DEPOSIT_CREATE", "DEPOSIT_VIEW", "ACTIVATE_TOUCH_GENERATE_CODE", "TASK_MANAGEMENT_VIEW",
                "TASK_MANAGEMENT_AUDIT_TRAIL", "LEAD_ASSIGNMENT_VIEW");

        Mockito.when(staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId(anyString()))
                .thenReturn(new V1PermissionResponse(permissionList));

        V1PermissionResponse response = permissionService.getPermission("");

        Assertions.assertEquals(permissionList, response.getPermissionList());
    }

    @Test
    void getPermissionSuccessSWhenRoleIdIsNullShouldReturnPermissionList() {
        Mockito.when(httpHeaders.getFirst(STAFF_ID)).thenReturn("testStaffId");

        StaffLoginInfoInquiry staffLoginInfoInquiry = new StaffLoginInfoInquiry();
        staffLoginInfoInquiry.setCnRole("TOP_BR_MAKER");

        List<String> permissionList = Arrays.asList("CREATE_RM_VIEW", "CREATE_RM_RECALL", "CREATE_RM_CREATE",
                "CREATE_RM_UPDATE", "CREATE_RM_CANCEL", "NEW_CAR_VIEW", "NEW_CAR_CREATE", "NEW_CAR_UPDATE",
                "NEW_CAR_CANCEL", "DEPOSIT_CREATE", "DEPOSIT_VIEW", "ACTIVATE_TOUCH_GENERATE_CODE", "TASK_MANAGEMENT_VIEW",
                "TASK_MANAGEMENT_AUDIT_TRAIL", "LEAD_ASSIGNMENT_VIEW");

        Mockito.when(staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId(anyString()))
                .thenReturn(new V1PermissionResponse(permissionList));

        V1PermissionResponse response = permissionService.getPermission("testRoleId");

        Assertions.assertEquals(permissionList, response.getPermissionList());
    }

    @Test
    void getPermissionMissingMandatoryHeaderShouldThrowBadRequest() {
        Assertions.assertThrows(BadRequestException.class, () -> permissionService.getPermission(""));
    }

}
