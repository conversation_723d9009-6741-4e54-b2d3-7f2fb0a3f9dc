package com.ttb.top.rbacservice.service;

import com.ttb.top.library.exceptionmodel.exception.CommonException;
import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.rbacservice.model.StaffRolePermissionConfig;
import com.ttb.top.rbacservice.model.V1PermissionResponse;
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository;
import java.io.IOException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.DATA_NOT_FOUND;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
public class StaffRolePermissionConfigCacheServiceTest {
    @InjectMocks
    StaffRolePermissionConfigCacheService staffRolePermissionConfigCacheService;

    @Mock
    StaffRolePermissionConfigRepository staffRolePermissionConfigRepository;

    List<Object[]> mockResults = new ArrayList<>();

    @Test
    void getStaffRolePermissionConfigFromPostgreSqlSuccessShouldReturnPermissionList() {
        String[] permissionArr = new String[]{"CREATE_RM_VIEW", "CREATE_RM_RECALL", "CREATE_RM_CREATE",
            "CREATE_RM_UPDATE", "CREATE_RM_CANCEL", "NEW_CAR_VIEW", "NEW_CAR_CREATE", "NEW_CAR_UPDATE",
            "NEW_CAR_CANCEL", "DEPOSIT_CREATE", "DEPOSIT_VIEW", "ACTIVATE_TOUCH_GENERATE_CODE", "TASK_MANAGEMENT_VIEW",
            "TASK_MANAGEMENT_AUDIT_TRAIL", "LEAD_ASSIGNMENT_VIEW"};

        mockResults.add(new Object[]{"roledId", permissionArr});

        List<String> permissionList = Arrays.asList("CREATE_RM_VIEW", "CREATE_RM_RECALL", "CREATE_RM_CREATE",
                "CREATE_RM_UPDATE", "CREATE_RM_CANCEL", "NEW_CAR_VIEW", "NEW_CAR_CREATE", "NEW_CAR_UPDATE",
                "NEW_CAR_CANCEL", "DEPOSIT_CREATE", "DEPOSIT_VIEW", "ACTIVATE_TOUCH_GENERATE_CODE", "TASK_MANAGEMENT_VIEW",
                "TASK_MANAGEMENT_AUDIT_TRAIL", "LEAD_ASSIGNMENT_VIEW");

        Mockito.when(staffRolePermissionConfigRepository.findByRoleId(anyString())).thenReturn(mockResults);
        V1PermissionResponse result = staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId("roleId");
        Assertions.assertEquals(permissionList, result.getPermissionList());
    }

    @Test
    void getStaffRolePermissionConfigFromPostgreSqlErrorShouldThrowDatabaseErrorException() {
        Mockito.when(staffRolePermissionConfigRepository.findByRoleId(anyString())).thenThrow(RuntimeException.class);
        Assertions.assertThrows(DatabaseErrorException.class, () -> staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId("roleId"));
    }

    @Test
    void getStaffRolePermissionConfigFromPostgreSqlNoPermissionDataShouldReturnDataNotFound() {
        String[] permissionArr = new String[]{"CREATE_RM_VIEW", "CREATE_RM_RECALL", "CREATE_RM_CREATE",
                "CREATE_RM_UPDATE", "CREATE_RM_CANCEL", "NEW_CAR_VIEW", "NEW_CAR_CREATE", "NEW_CAR_UPDATE",
                "NEW_CAR_CANCEL", "DEPOSIT_CREATE", "DEPOSIT_VIEW", "ACTIVATE_TOUCH_GENERATE_CODE", "TASK_MANAGEMENT_VIEW",
                "TASK_MANAGEMENT_AUDIT_TRAIL", "LEAD_ASSIGNMENT_VIEW"};

        List<String> permissionList = Arrays.asList("CREATE_RM_VIEW", "CREATE_RM_RECALL", "CREATE_RM_CREATE",
                "CREATE_RM_UPDATE", "CREATE_RM_CANCEL", "NEW_CAR_VIEW", "NEW_CAR_CREATE", "NEW_CAR_UPDATE",
                "NEW_CAR_CANCEL", "DEPOSIT_CREATE", "DEPOSIT_VIEW", "ACTIVATE_TOUCH_GENERATE_CODE", "TASK_MANAGEMENT_VIEW",
                "TASK_MANAGEMENT_AUDIT_TRAIL", "LEAD_ASSIGNMENT_VIEW");


        mockResults.add(new Object[]{"roledId", null});

        Mockito.when(staffRolePermissionConfigRepository.findByRoleId(anyString())).thenReturn(mockResults);

        String errCode = Assertions.assertThrows(CommonException.class, () -> staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId("roleId")).getErrorCode();
        Assertions.assertEquals(errCode, DATA_NOT_FOUND.getCode());
    }

    @Test
    void getStaffRolePermissionConfigFromPostgreSqlGotEmpty() {
        Mockito.when(staffRolePermissionConfigRepository.findByRoleId(anyString())).thenReturn(new ArrayList<>());
        Assertions.assertThrows(CommonException.class, () -> staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId("roleId"));
    }

}
