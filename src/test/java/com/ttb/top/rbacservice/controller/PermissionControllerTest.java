package com.ttb.top.rbacservice.controller;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.rbacservice.model.V1PermissionResponse;
import com.ttb.top.rbacservice.service.PermissionService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.GENERIC_SUCCESS_CODE;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
public class PermissionControllerTest {
    @Mock
    PermissionService permissionService;

    @InjectMocks
    PermissionController permissionController;

    @Test
    void getPermissionSuccessShouldReturnSuccess() {
        List<String> permissionList = Arrays.asList("CREATE_RM_VIEW", "CREATE_RM_RECALL", "CREATE_RM_CREATE",
                "CREATE_RM_UPDATE", "CREATE_RM_CANCEL", "NEW_CAR_VIEW", "NEW_CAR_CREATE", "NEW_CAR_UPDATE",
                "NEW_CAR_CANCEL", "DEPOSIT_CREATE", "DEPOSIT_VIEW", "ACTIVATE_TOUCH_GENERATE_CODE", "TASK_MANAGEMENT_VIEW",
                "TASK_MANAGEMENT_AUDIT_TRAIL", "LEAD_ASSIGNMENT_VIEW");
        V1PermissionResponse data = new V1PermissionResponse(permissionList);
        Mockito.when(permissionService.getPermission(anyString())).thenReturn(data);
        ResponseModel<V1PermissionResponse> responseModel = permissionController.getPermission("test");
        Assertions.assertEquals(GENERIC_SUCCESS_CODE.getCode(), responseModel.getStatus().getCode());
        Assertions.assertEquals(permissionList, responseModel.getDataObj().getPermissionList());
    }


}
