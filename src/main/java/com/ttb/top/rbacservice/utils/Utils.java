package com.ttb.top.rbacservice.utils;

import static java.time.format.DateTimeFormatter.ofPattern;

import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import java.time.OffsetDateTime;
import lombok.experimental.UtilityClass;

@UtilityClass
public class Utils {

    public static String getCurrentDatetime(String expectedFormat) {
        return OffsetDateTime.now().format(ofPattern(expectedFormat));
    }

    public static String logCircuitBreakerErrorMessage(CallNotPermittedException ex) {
        return String.format("Circuit breaker[%s] error: %s", ex.getCausingCircuitBreakerName(), ex.getMessage());
    }
}
