package com.ttb.top.rbacservice.utils;

import static com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant.X_CORRELATION_ID;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.APP_ID;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.APP_PASSWORD;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.ISO_OFFSET_DATETIME_FORMAT;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.REQUEST_DATETIME;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.REQUEST_UUID;

import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FeuamUtils {

    @Value("${feuam.credentials.app-id}")
    private final String appId;
    @Value("${feuam.credentials.app-password}")
    private final String appPassword;

    public HttpHeaders buildHttpHeaders(HttpHeaders httpHeaders) {
        HttpHeaders feuamHttpHeaders = new HttpHeaders();
        feuamHttpHeaders.set(APP_ID, appId);
        feuamHttpHeaders.set(APP_PASSWORD, appPassword);
        feuamHttpHeaders.set(REQUEST_UUID,
            Optional.ofNullable(httpHeaders.getFirst(X_CORRELATION_ID)).orElse(UUID.randomUUID().toString()));
        feuamHttpHeaders.set(REQUEST_DATETIME, Utils.getCurrentDatetime(ISO_OFFSET_DATETIME_FORMAT));

        /*Remove herder: authorization*/
        feuamHttpHeaders.remove(HttpHeaders.AUTHORIZATION);

        return feuamHttpHeaders;
    }
}
