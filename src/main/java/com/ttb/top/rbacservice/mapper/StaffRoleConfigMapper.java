package com.ttb.top.rbacservice.mapper;

import com.ttb.top.rbacservice.model.StaffRoleConfigInquiryResponse;
import com.ttb.top.rbacservice.model.entity.StaffRoleConfigEntity;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;

import java.util.List;

import static org.mapstruct.MappingConstants.ComponentModel.SPRING;
import static org.mapstruct.NullValueCheckStrategy.ALWAYS;

@Mapper(
        componentModel = SPRING,
        nullValueCheckStrategy = ALWAYS,
        builder = @Builder(disableBuilder = true)
)
public interface StaffRoleConfigMapper {

    List<StaffRoleConfigInquiryResponse.staffRole> mapEntitiesToStaffRoles(
            List<StaffRoleConfigEntity> entities
    );

}
