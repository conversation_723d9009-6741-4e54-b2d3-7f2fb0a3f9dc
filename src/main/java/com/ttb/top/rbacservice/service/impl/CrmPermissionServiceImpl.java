package com.ttb.top.rbacservice.service.impl;

import com.ttb.top.library.crmapihelper.model.CrmResponseModel;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.exceptionmodel.exception.CommonException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.library.exceptionmodel.exception.LegacySystemFailureException;
import com.ttb.top.rbacservice.feign.CrmsspServiceClient;
import com.ttb.top.rbacservice.model.CrmPermissionInquiryResponse;
import com.ttb.top.rbacservice.model.StaffLoginInfo;
import com.ttb.top.rbacservice.model.feign.crmssp.UserRetrieveUserInfoRequest;
import com.ttb.top.rbacservice.model.feign.crmssp.UserRetrieveUserInfoResponse;
import com.ttb.top.rbacservice.service.CacheService;
import com.ttb.top.rbacservice.service.CrmPermissionService;
import feign.FeignException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.DATABASE_ERROR_CODE;
import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.DATA_NOT_FOUND;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.STAFF_ID;

@Service
@Slf4j
@RequiredArgsConstructor
public class CrmPermissionServiceImpl implements CrmPermissionService {

    private final HttpHeaders httpHeaders;
    private final CacheService cacheService;
    private final CrmsspServiceClient crmsspServiceClient;

    @Override
    public CrmPermissionInquiryResponse getPermission() {
        validateRequestField();
        StaffLoginInfo staffLoginInfo = cacheService.getCRMStaffRolePermissionWithStaffId(httpHeaders.getFirst(STAFF_ID));

        if (Objects.isNull(staffLoginInfo)) {
            try {
                CrmResponseModel<UserRetrieveUserInfoResponse> response
                        = crmsspServiceClient.userRetrieveUserInfo(new UserRetrieveUserInfoRequest()
                        .setEmployeeId(httpHeaders.getFirst(STAFF_ID)));
                log.info("CRM Response: {}", response);
                staffLoginInfo = cacheService.setCRMStaffRolePermissionWithStaffId(httpHeaders.getFirst(STAFF_ID),
                        response.getDataObj());
            } catch (CallNotPermittedException e) {
                log.error("CallNotPermittedException error: ", e);
                throw new GenericException(ResponseCodeEnum.CIRCUIT_BREAKER_CODE);
            } catch (FeignException e) {
                log.error("FeignException error: ", e);
                if (DATA_NOT_FOUND.getHttpStatus().value() == e.status()) {
                    throw new CommonException(DATA_NOT_FOUND, e.getMessage());
                }
                throw new LegacySystemFailureException(e);
            }
        }

        return new CrmPermissionInquiryResponse()
                .setEmail(staffLoginInfo.getEmail())
                .setFirstname(staffLoginInfo.getFirstname())
                .setLastname(staffLoginInfo.getLastname())
                .setTeams(staffLoginInfo.getTeams())
                .setProfile(staffLoginInfo.getProfile());
    }

    private void validateRequestField() {
        if (StringUtils.isEmpty(httpHeaders.getFirst(STAFF_ID))) {
            throw new BadRequestException();
        }
    }
}
