package com.ttb.top.rbacservice.service.impl;

import static com.ttb.top.rbacservice.constant.RbacServiceConstant.BRANCH_CODE_0000000001;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.BRANCH_CODE_001;

import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException;
import com.ttb.top.rbacservice.helper.MicrosoftApiHelper;
import com.ttb.top.rbacservice.model.StaffInfo;
import com.ttb.top.rbacservice.model.StaffListInquiryRequest;
import com.ttb.top.rbacservice.model.StaffListInquiryResponse;
import com.ttb.top.rbacservice.model.entity.MovementStaffListEntity;
import com.ttb.top.rbacservice.model.microsoft.GraphApiGroupDetailResponse;
import com.ttb.top.rbacservice.model.microsoft.GraphApiMemberDetailResponse;
import com.ttb.top.rbacservice.repository.MovementStaffListRepository;
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository;
import com.ttb.top.rbacservice.service.CacheService;
import com.ttb.top.rbacservice.service.StaffListService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaffListServiceImpl implements StaffListService {

    private final CacheService cacheService;
    private final MicrosoftApiHelper microsoftApiHelper;
    private final StaffRolePermissionConfigRepository staffRolePermissionConfigRepository;
    private final MovementStaffListRepository movementStaffListRepository;

    @Override
    public StaffListInquiryResponse inquiryStaffList(StaffListInquiryRequest request) {
        List<String> finalStaffList = new ArrayList<>();

        /*Inquiry roles from Database*/
        List<String> roleList = getRolesByPermissions(request.getPermissionList());
        List<MovementStaffListEntity> movementStaffListFromCache = cacheService
                .getMomentStaffList();
        List<MovementStaffListEntity> movementStaffListFromDb = movementStaffListRepository
                .findAll();
        Set<String> employeeIdsFromCache = Optional.ofNullable(movementStaffListFromCache)
                .orElse(new ArrayList<>())
                .stream()
                .map(MovementStaffListEntity::getEmployeeId)
                .collect(Collectors.toSet());
        Set<String> employeeIdsFromDb = movementStaffListFromDb.stream()
                .map(MovementStaffListEntity::getEmployeeId)
                .collect(Collectors.toSet());
        if (!employeeIdsFromCache.equals(employeeIdsFromDb) || movementStaffListFromCache == null) {
            cacheService.setMomentStaffList(movementStaffListFromDb);
            /*Set branchCode to each Staff-Info*/
            finalStaffList = handleStaffListFromCacheIsEmptyOrNotMatchWithMomentStaffDb(
                    request,
                    roleList,
                    movementStaffListFromDb,
                    employeeIdsFromDb).stream().map(StaffInfo::getEmployeeId).toList();
        } else if (employeeIdsFromCache.equals(employeeIdsFromDb)) {
            finalStaffList = roleList.stream()
                    .flatMap(role -> cacheService.getAllStaffWithinRole(request.getBranchCode(), role).stream())
                    .map(StaffInfo::getEmployeeId).toList();
        } else {
            log.warn("No conditions match.");
        }

        return StaffListInquiryResponse.builder()
                .staffList(finalStaffList.stream().distinct().toList())
                .build();
    }

    private List<StaffInfo> handleStaffListFromCacheIsEmptyOrNotMatchWithMomentStaffDb(
            StaffListInquiryRequest request,
            List<String> roleList,
            List<MovementStaffListEntity> movementStaffListFromDb,
            Set<String> employeeIdsFromDb
    ) {
        List<StaffInfo> finalStaffList = new ArrayList<>();
        /*Inquiry groups by role-ids from Graph APIs*/
        List<GraphApiGroupDetailResponse> groupList = microsoftApiHelper.getGroupsByRoleIds(roleList);
        /*Inquiry all members by groups*/
        Map<String, List<GraphApiMemberDetailResponse>> mapMemersByGroup =
            microsoftApiHelper.getMembersByGroupId(groupList.stream().map(GraphApiGroupDetailResponse::getId).toList());

        for (GraphApiGroupDetailResponse group : groupList) {
            List<StaffInfo> finalStaffListEachGroup = new ArrayList<>();
            /*Get employee-ids that have permission in the request branch excluding the movement staff*/
            List<GraphApiMemberDetailResponse> staffListFromAdEachGroup = mapMemersByGroup.get(group.getId());

            List<GraphApiMemberDetailResponse> branchMembersEachGroupWoMovement = staffListFromAdEachGroup.stream()
                    .filter(staff -> {
                        if(StringUtils.equals(BRANCH_CODE_001, request.getBranchCode())) {
                            return StringUtils.contains(staff.getMsExchExtensionAttribute27(), BRANCH_CODE_0000000001)
                                && !employeeIdsFromDb.contains(staff.getOnPremisesSamAccountName());

                        }

                        return Optional.ofNullable(Optional.ofNullable(staff.getOnPremisesExtensionAttributes())
                            .orElse(new GraphApiMemberDetailResponse.OnPremisesExtensionAttributes())
                            .getExtensionAttribute10())
                            .orElse(StringUtils.EMPTY)
                            .contains(request.getBranchCode()) &&
                            !employeeIdsFromDb.contains(staff.getOnPremisesSamAccountName());
                    })
                    .toList();
            /*Get employee-ids that have permission and will move to request branch*/
            List<String> employeeListThatMoveToTheBranchEachGroup = movementStaffListFromDb
                    .stream()
                    .filter(staff -> StringUtils.equals(request.getBranchCode(), staff.getBranchCode()))
                    .map(MovementStaffListEntity::getEmployeeId)
                    .toList();
            List<GraphApiMemberDetailResponse> movementMembersEachGroup = staffListFromAdEachGroup
                    .stream()
                    .filter(staff ->
                            employeeListThatMoveToTheBranchEachGroup
                                    .contains(staff.getOnPremisesSamAccountName()))
                    .toList();

            finalStaffListEachGroup.addAll(
                    branchMembersEachGroupWoMovement
                            .stream()
                            .map(staff ->
                                    new StaffInfo(staff.getOnPremisesSamAccountName(),
                                            request.getBranchCode(),
                                            group.getDisplayName()))
                            .toList()
            );
            finalStaffListEachGroup.addAll(
                    movementMembersEachGroup
                            .stream()
                            .map(staff ->
                                    new StaffInfo(staff.getOnPremisesSamAccountName(),
                                            request.getBranchCode(),
                                            group.getDisplayName()))
                            .toList()
            );

            /*Set role to each staff-info*/
            cacheService.setAllStaffWithinRole(request.getBranchCode(), group.getDisplayName(),
                    finalStaffListEachGroup);
            finalStaffList.addAll(finalStaffListEachGroup);
        }
        return finalStaffList;
    }

    private List<String> getRolesByPermissions(List<String> permissions) {
        try {
            return staffRolePermissionConfigRepository.findByPermissionIdIn(permissions);
        } catch (Exception ex) {
            log.error("An error occurred while trying to query data: ", ex);
            throw new DatabaseErrorException();
        }
    }
}
