package com.ttb.top.rbacservice.service;

import com.ttb.top.rbacservice.model.StaffInfo;
import com.ttb.top.rbacservice.model.StaffLoginInfo;
import com.ttb.top.rbacservice.model.entity.MovementStaffListEntity;

import java.util.List;

public interface CacheService {

    List<StaffInfo> getAllStaffWithinRole(String branchCode, String roleId);

    List<StaffInfo> setAllStaffWithinRole(String branchCode, String roleId, List<StaffInfo> value);

    List<MovementStaffListEntity> getMomentStaffList();

    List<MovementStaffListEntity> setMomentStaffList(List<MovementStaffListEntity>
            movementStaffListEntityList);

    StaffLoginInfo getCRMStaffRolePermissionWithStaffId(String staffId);

    StaffLoginInfo setCRMStaffRolePermissionWithStaffId(String staffId, StaffLoginInfo value);
}
