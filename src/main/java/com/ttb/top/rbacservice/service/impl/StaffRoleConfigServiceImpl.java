package com.ttb.top.rbacservice.service.impl;

import com.ttb.top.rbacservice.mapper.StaffRoleConfigMapper;
import com.ttb.top.rbacservice.model.StaffRoleConfigInquiryResponse;
import com.ttb.top.rbacservice.model.entity.StaffRoleConfigEntity;
import com.ttb.top.rbacservice.service.StaffRoleConfigCacheService;
import com.ttb.top.rbacservice.service.StaffRoleConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaffRoleConfigServiceImpl implements StaffRoleConfigService {

    private final StaffRoleConfigMapper staffRoleConfigMapper;
    private final StaffRoleConfigCacheService staffRoleConfigCacheService;

    @Override
    public StaffRoleConfigInquiryResponse inquiryStaffRoleConfig() {
        List<StaffRoleConfigEntity> staffRoleConfigEntities = staffRoleConfigCacheService.inquiryStaffRoleConfig();
        return StaffRoleConfigInquiryResponse.builder()
                .staffRole(staffRoleConfigMapper.mapEntitiesToStaffRoles(staffRoleConfigEntities))
                .build();
    }

}
