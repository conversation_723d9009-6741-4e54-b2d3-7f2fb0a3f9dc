package com.ttb.top.rbacservice.service.impl;

import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.rbacservice.model.entity.StaffRoleConfigEntity;
import com.ttb.top.rbacservice.repository.StaffRoleConfigRepository;
import com.ttb.top.rbacservice.service.StaffRoleConfigCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.DATA_NOT_FOUND;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.REDIS_CACHE_MANAGER_NAME;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaffRoleConfigCacheServiceImpl implements StaffRoleConfigCacheService {

    private final StaffRoleConfigRepository staffRoleConfigRepository;

    @Override
    @Cacheable(
            value = "staff_role_config",
            key = "'staff_role_config'",
            cacheManager = REDIS_CACHE_MANAGER_NAME,
            unless = "#result == null or #result.size()==0"
    )
    public List<StaffRoleConfigEntity> inquiryStaffRoleConfig() {
        log.debug("Inquiry staff role config");
        List<StaffRoleConfigEntity> staffRoleConfigEntity;
        try {
            staffRoleConfigEntity = staffRoleConfigRepository.findAll();
        } catch (Exception ex) {
            log.error("Database error while inquiry staff role config");
            throw new DatabaseErrorException(ex);
        }
        if (CollectionUtils.isEmpty(staffRoleConfigEntity)) {
            log.error("Date not found");
            throw new GenericException(DATA_NOT_FOUND);
        }
        return staffRoleConfigEntity;
    }

}
