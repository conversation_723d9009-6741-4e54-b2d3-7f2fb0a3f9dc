package com.ttb.top.rbacservice.service;

import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.utilityhelper.util.CommonUtil;
import com.ttb.top.rbacservice.feign.AuthServiceClient;
import com.ttb.top.rbacservice.model.V1PermissionResponse;
import com.ttb.top.rbacservice.model.feign.StaffLoginInfoInquiry;
import io.micrometer.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.GENERIC_ERROR_CODE;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.STAFF_ID;

@Service
public class PermissionService {
    @Autowired
    HttpHeaders httpHeaders;

    @Autowired
    AuthServiceClient authServiceClient;

    @Autowired
    StaffRolePermissionConfigCacheService staffRolePermissionConfigCacheService;


    public V1PermissionResponse getPermission(String requestRoleId) {
        String roleId;

        if (StringUtils.isEmpty(httpHeaders.getFirst(STAFF_ID))) {
            throw new BadRequestException();
        }

        if (StringUtils.isEmpty(requestRoleId)) {
            StaffLoginInfoInquiry staffLoginInfoInquiry = CommonUtil.unwrapResponseModel(GENERIC_ERROR_CODE.getCode(),
                    authServiceClient.staffLoginInfoInquiry(httpHeaders));
            roleId = staffLoginInfoInquiry.getCnRole();
        } else {
            roleId = requestRoleId;
        }

        return staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId(roleId);
    }
}
