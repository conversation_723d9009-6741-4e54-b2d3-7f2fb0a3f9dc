package com.ttb.top.rbacservice.service.impl;

import com.ttb.top.rbacservice.model.StaffInfo;
import com.ttb.top.rbacservice.model.StaffLoginInfo;
import com.ttb.top.rbacservice.model.entity.MovementStaffListEntity;
import com.ttb.top.rbacservice.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.ttb.top.rbacservice.constant.RbacServiceConstant.RBAC_CACHE_NAME;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.RBAC_CRM_CACHE_NAME;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.REDIS_CACHE_MANAGER_NAME;

@Slf4j
@Service
public class CacheServiceImpl implements CacheService {

    @Override
    @Cacheable(
        value = RBAC_CACHE_NAME,
        key = "'staff_within_role:'+#branchCode+':'+#roleId",
        cacheManager = REDIS_CACHE_MANAGER_NAME,
        unless = "true"
    )
    public List<StaffInfo> getAllStaffWithinRole(String branchCode, String roleId) {
        return List.of();
    }

    @Override
    @CachePut(
        value = RBAC_CACHE_NAME,
        key = "'staff_within_role:'+#branchCode+':'+#roleId",
        cacheManager = REDIS_CACHE_MANAGER_NAME,
        condition = "#branchCode != null and #roleId != null and #value != null and #value.size() > 0"
    )
    public List<StaffInfo> setAllStaffWithinRole(String branchCode, String roleId, List<StaffInfo> value) {
        log.info("Put cache: rbac::staff_within_role:{}:{}", branchCode, roleId);
        return value;
    }

    @Cacheable(
            value = RBAC_CACHE_NAME,
            key = "'movement_staff'",
            cacheManager = REDIS_CACHE_MANAGER_NAME,
            unless = "true"
    )
    public List<MovementStaffListEntity> getMomentStaffList() {
        return null;
    }

    @CachePut(
            value = RBAC_CACHE_NAME,
            key = "'movement_staff'",
            cacheManager = REDIS_CACHE_MANAGER_NAME,
            unless = "#result == null"
    )
    public List<MovementStaffListEntity> setMomentStaffList(List<MovementStaffListEntity>
                                                                                movementStaffListEntityList) {
        log.info("Put cache: rbac::movement_staff");
        return movementStaffListEntityList;
    }

    @Cacheable(
            value = RBAC_CRM_CACHE_NAME,
            key = "'crm_staff_role_permission_'+#staffId",
            cacheManager = REDIS_CACHE_MANAGER_NAME,
            unless = "true"
    )
    public StaffLoginInfo getCRMStaffRolePermissionWithStaffId(String staffId) {
        return null;
    }

    @CachePut(
            value = RBAC_CRM_CACHE_NAME,
            key = "'crm_staff_role_permission_'+#staffId",
            cacheManager = REDIS_CACHE_MANAGER_NAME,
            unless = "#staffId == null"
    )
    public StaffLoginInfo setCRMStaffRolePermissionWithStaffId(String staffId, StaffLoginInfo value) {
        log.info("Put cache: rbac_crm::crm_staff_role_permission:{}:{}", staffId, value);
        return value;
    }

}
