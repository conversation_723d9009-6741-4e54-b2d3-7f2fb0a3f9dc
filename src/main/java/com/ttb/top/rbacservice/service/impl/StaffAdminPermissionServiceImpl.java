package com.ttb.top.rbacservice.service.impl;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.GENERIC_ERROR_CODE;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.STAFF_ID;

import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.rbacservice.feign.AdminServiceClient;
import com.ttb.top.rbacservice.model.PermissionInquiryRequest;
import com.ttb.top.rbacservice.model.V1PermissionResponse;
import com.ttb.top.rbacservice.model.feign.StaffAdminInquiryRequest;
import com.ttb.top.rbacservice.model.feign.StaffAdminInquiryResponse;
import com.ttb.top.rbacservice.service.StaffAdminPermissionService;
import com.ttb.top.rbacservice.service.StaffRolePermissionConfigCacheService;
import com.ttb.top.library.utilityhelper.util.CommonUtil;
import io.micrometer.common.util.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class
StaffAdminPermissionServiceImpl implements StaffAdminPermissionService {

    HttpHeaders httpHeaders;

    AdminServiceClient adminServiceClient;

    StaffRolePermissionConfigCacheService staffRolePermissionConfigCacheService;


    public V1PermissionResponse getPermission(PermissionInquiryRequest permissionInquiryRequest) {
        String roleId;

        if (StringUtils.isEmpty(permissionInquiryRequest.getStaffId())) {
            throw new BadRequestException();
        }

        if (StringUtils.isEmpty(permissionInquiryRequest.getRoleId())) {
            StaffAdminInquiryResponse staffAdminInfoInquiryResponse = CommonUtil.unwrapResponseModel(
                    GENERIC_ERROR_CODE.getCode(),
                    adminServiceClient.staffAdminInfoInquiry(
                            httpHeaders, StaffAdminInquiryRequest
                                    .builder().
                                    staffId(permissionInquiryRequest.getStaffId())
                                    .build()
                    ));
            roleId = staffAdminInfoInquiryResponse.getCnRole();
        } else {
            roleId = permissionInquiryRequest.getRoleId();
        }

        return staffRolePermissionConfigCacheService.getStaffRolePermissionConfigRoleId(roleId);
    }
}
