package com.ttb.top.rbacservice.service;

import com.ttb.top.library.exceptionmodel.exception.CommonException;
import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException;
import com.ttb.top.rbacservice.model.StaffRolePermissionConfig;
import com.ttb.top.rbacservice.model.V1PermissionResponse;
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.DATA_NOT_FOUND;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.REDIS_CACHE_MANAGER_NAME;

@Slf4j
@Service
public class StaffRolePermissionConfigCacheService {

    @Autowired
    StaffRolePermissionConfigRepository staffRolePermissionConfigRepository;

    @Cacheable(value = "staff_role_permission_config_role_id", key = "'staff_role_permission_config_'.concat(#roleId)", cacheManager = REDIS_CACHE_MANAGER_NAME, unless = "#result == null")
    public V1PermissionResponse getStaffRolePermissionConfigRoleId(String roleId) {
        List<StaffRolePermissionConfig> staffRolePermissionConfigList;
        try {
            log.info("query staffRolePermissionConfigRepository with roleId:{}", roleId);
            List<Object[]> data = staffRolePermissionConfigRepository.findByRoleId(roleId);
            if (ObjectUtils.isNotEmpty(data)) {
                staffRolePermissionConfigList = data.stream().map(item -> new StaffRolePermissionConfig(
                    (String) item[0], // roleId
                    item[1] // permissions
                )).collect(Collectors.toList());
            } else {
                staffRolePermissionConfigList = new ArrayList<>();
            }
            log.info("staffRolePermissionConfigList ==> {}", staffRolePermissionConfigList);
        } catch (Exception ex) {
            throw new DatabaseErrorException();
        }

        if (ObjectUtils.isEmpty(staffRolePermissionConfigList) || ObjectUtils.isEmpty(
                staffRolePermissionConfigList.get(0).getPermissions())) {
            throw new CommonException(DATA_NOT_FOUND.getCode());
        }
        return new V1PermissionResponse(convertStringArrayToList(
                (String[]) staffRolePermissionConfigList.get(0).getPermissions()));
    }

    private List<String> convertStringArrayToList(String[] items) {
        return new ArrayList<>(Arrays.asList(items));
    }
}
