package com.ttb.top.rbacservice.service.impl;

import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.rbacservice.configuration.UnmarkRoleConfig;
import com.ttb.top.rbacservice.model.UnmarkRoleResponse;
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository;
import com.ttb.top.rbacservice.service.UnmarkRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnmarkRoleServiceImpl implements UnmarkRoleService {

    private final HttpHeaders httpHeaders;
    private final UnmarkRoleConfig unmarkRoleConfig;
    private final StaffRolePermissionConfigRepository staffRolePermissionConfigRepository;

    @Override
    public UnmarkRoleResponse getUnmaskRoles() {

        validateRequestField();

        if ("TEP_WEB".equals(httpHeaders.getFirst("channel"))) {

            return UnmarkRoleResponse.builder()
                    .roles(staffRolePermissionConfigRepository.findByPermissionId(unmarkRoleConfig.getUnmarkPermissionWebName()))
                    .build();
        }
        return UnmarkRoleResponse.builder()
                .roles(staffRolePermissionConfigRepository.findByPermissionId(unmarkRoleConfig.getUnmarkPermissionTabletName()))
                .build();
    }

    void validateRequestField() {
        if (httpHeaders.getFirst("STAFF-ID") == null
                || httpHeaders.getFirst("channel") == null) {
            throw new BadRequestException();
        }
    }
}
