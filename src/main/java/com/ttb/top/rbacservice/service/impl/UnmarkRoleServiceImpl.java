package com.ttb.top.rbacservice.service.impl;

import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.rbacservice.configuration.UnmarkRoleConfig;
import com.ttb.top.rbacservice.model.UnmarkRoleResponse;
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository;
import com.ttb.top.rbacservice.service.UnmarkRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnmarkRoleServiceImpl implements UnmarkRoleService {

    private final HttpHeaders httpHeaders;
    private final UnmarkRoleConfig unmarkRoleConfig;
    private final StaffRolePermissionConfigRepository staffRolePermissionConfigRepository;

    @Override
    public UnmarkRoleResponse getUnmaskRoles() {

        validateRequestField();
        if ("TEP_WEB".equals(httpHeaders.getFirst("channel"))) {
            return buildResponse(staffRolePermissionConfigRepository.findByPermissionId(
                    unmarkRoleConfig.getUnmarkPermissionWebName()));
        }
       return buildResponse(staffRolePermissionConfigRepository.findByPermissionId(
                unmarkRoleConfig.getUnmarkPermissionTabletName()));
    }

    private UnmarkRoleResponse buildResponse(List<String> roles){
        if (roles.isEmpty()) {
            return UnmarkRoleResponse.builder()
                    .roles(List.of())
                    .build();
        }
        return UnmarkRoleResponse.builder()
                .roles(roles)
                .build();
    }

    void validateRequestField() {
        if (httpHeaders.getFirst("STAFF-ID") == null
                || httpHeaders.getFirst("channel") == null) {
            throw new BadRequestException();
        }
    }
}
