package com.ttb.top.rbacservice.service.impl;

import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.exceptionmodel.exception.DatabaseErrorException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.rbacservice.configuration.UnmarkRoleConfig;
import com.ttb.top.rbacservice.model.UnmarkRoleResponse;
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository;
import com.ttb.top.rbacservice.service.UnmarkRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.JDBCException;
import org.springframework.dao.InvalidDataAccessResourceUsageException;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnmarkRoleServiceImpl implements UnmarkRoleService {

    private final HttpHeaders httpHeaders;
    private final UnmarkRoleConfig unmarkRoleConfig;
    private final StaffRolePermissionConfigRepository staffRolePermissionConfigRepository;

    @Override
    public UnmarkRoleResponse getUnmaskRoles() {

        validateRequestField();

        if ("TEP_WEB".equals(httpHeaders.getFirst("channel"))) {
            return getUnmaskRolesFromDB(unmarkRoleConfig.getUnmarkPermissionWebName());
        }
        return  getUnmaskRolesFromDB(unmarkRoleConfig.getUnmarkPermissionTabletName());
    }

    private UnmarkRoleResponse getUnmaskRolesFromDB(String unmarkPermissionConfig) {

        try {
            return UnmarkRoleResponse.builder()
                    .roles(staffRolePermissionConfigRepository.findByPermissionId(unmarkPermissionConfig))
                    .build();
        }
        catch (DatabaseErrorException e) {
            log.error("Database error while inquiry unmask role config",e);
            throw new DatabaseErrorException(e);
        }
        catch (InvalidDataAccessResourceUsageException e){
            log.error("InvalidDataAccessResourceUsageException error while inquiry unmask role config",e);
            throw new DatabaseErrorException(e);
        }
        catch (Exception e) {
            log.error("An error occurred while trying to query data: ", e);
            throw new GenericException(e);
        }
    }

    void validateRequestField() {
        if (httpHeaders.getFirst("STAFF-ID") == null
                || httpHeaders.getFirst("channel") == null) {
            throw new BadRequestException();
        }
    }
}
