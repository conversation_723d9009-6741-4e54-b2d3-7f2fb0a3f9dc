package com.ttb.top.rbacservice.service.impl;

import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.BadRequestException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.rbacservice.configuration.UnmarkRoleConfig;
import com.ttb.top.rbacservice.model.UnmarkRoleResponse;
import com.ttb.top.rbacservice.repository.StaffRolePermissionConfigRepository;
import com.ttb.top.rbacservice.service.UnmarkRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnmarkRoleServiceImpl implements UnmarkRoleService {

    private final HttpHeaders httpHeaders;
    private final UnmarkRoleConfig unmarkRoleConfig;
    private final StaffRolePermissionConfigRepository staffRolePermissionConfigRepository;

    @Override
    public UnmarkRoleResponse getUnmaskRoles() {

        validateRequestField();
        ArrayList<String> roles = new ArrayList<>();
        if ("TEP_WEB".equals(httpHeaders.getFirst("channel"))) {

            roles.addAll(staffRolePermissionConfigRepository.findByPermissionId(
                    unmarkRoleConfig.getUnmarkPermissionWebName()));
        }
        roles.addAll(staffRolePermissionConfigRepository.findByPermissionId(
                unmarkRoleConfig.getUnmarkPermissionTabletName()));
        if (roles.isEmpty()) {
            throw new GenericException(ResponseCodeEnum.UNAUTHORIZED_CODE);
        }
        return UnmarkRoleResponse.builder()
                .roles(roles)
                .build();
    }

    void validateRequestField() {
        if (httpHeaders.getFirst("STAFF-ID") == null
                || httpHeaders.getFirst("channel") == null) {
            throw new BadRequestException();
        }
    }
}
