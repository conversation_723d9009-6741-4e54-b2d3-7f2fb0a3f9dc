package com.ttb.top.rbacservice.controller;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import com.ttb.top.rbacservice.model.StaffListInquiryRequest;
import com.ttb.top.rbacservice.model.StaffListInquiryResponse;
import com.ttb.top.rbacservice.service.StaffListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@EnableLookup
@RestController
@RequiredArgsConstructor
@RequestMapping("/staff-list")
public class StaffListController {

    private final StaffListService staffListService;

    @PostMapping("/inquiry")
    public ResponseModel<StaffListInquiryResponse> inquiryStaffList(
        @Valid @RequestBody StaffListInquiryRequest request
    ) {
        return ResponseModel.success(staffListService.inquiryStaffList(request));
    }
}
