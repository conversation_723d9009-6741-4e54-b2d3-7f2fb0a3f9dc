package com.ttb.top.rbacservice.controller;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import com.ttb.top.rbacservice.model.V1PermissionResponse;
import com.ttb.top.rbacservice.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping
@RestController
@EnableLookup
public class PermissionController {
    @Autowired
    PermissionService permissionService;

    @GetMapping("/permission")
    ResponseModel<V1PermissionResponse> getPermission(@RequestParam(value = "roleId", required = false) String roleId) {
        V1PermissionResponse data = permissionService.getPermission(roleId);
        return ResponseModel.success(data);
    }
}
