package com.ttb.top.rbacservice.controller;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import com.ttb.top.rbacservice.model.PermissionInquiryRequest;
import com.ttb.top.rbacservice.model.V1PermissionResponse;
import com.ttb.top.rbacservice.service.PermissionService;
import com.ttb.top.rbacservice.service.StaffAdminPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/staff-admin")
@RestController
@EnableLookup
public class StaffAdminPermissionController {

  @Autowired
  StaffAdminPermissionService staffAdminPermissionService;

  @PostMapping("/permission/inquiry")
  ResponseModel<V1PermissionResponse> getPermission(
      @RequestBody PermissionInquiryRequest permissionInquiryRequest) {
    V1PermissionResponse data = staffAdminPermissionService.getPermission(permissionInquiryRequest);
    return ResponseModel.success(data);
  }
}
