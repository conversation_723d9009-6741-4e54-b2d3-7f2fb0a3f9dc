package com.ttb.top.rbacservice.controller;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import com.ttb.top.rbacservice.model.StaffRoleConfigInquiryResponse;
import com.ttb.top.rbacservice.service.StaffRoleConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@EnableLookup
@RestController
@RequiredArgsConstructor
@RequestMapping("/staff-role-config")
public class StaffRoleConfigController {

    private final StaffRoleConfigService staffRoleConfigService;

    @GetMapping("/inquiry")
    public ResponseModel<StaffRoleConfigInquiryResponse> inquiryStaffRoleConfig() {
        return ResponseModel.success(staffRoleConfigService.inquiryStaffRoleConfig());
    }

}
