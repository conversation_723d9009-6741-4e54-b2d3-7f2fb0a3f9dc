package com.ttb.top.rbacservice.controller;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import com.ttb.top.rbacservice.model.CrmPermissionInquiryResponse;
import com.ttb.top.rbacservice.service.CrmPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/crm")
@RestController
@EnableLookup
@RequiredArgsConstructor
public class CrmPermissionController {

    private final CrmPermissionService crmPermissionService;

    @GetMapping("/permission")
    ResponseModel<CrmPermissionInquiryResponse> getPermission() {
        return ResponseModel.success(crmPermissionService.getPermission());
    }

}
