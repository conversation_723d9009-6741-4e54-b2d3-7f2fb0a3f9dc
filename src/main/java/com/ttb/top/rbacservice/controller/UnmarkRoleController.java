package com.ttb.top.rbacservice.controller;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import com.ttb.top.rbacservice.model.CrmPermissionInquiryResponse;
import com.ttb.top.rbacservice.model.UnmarkRoleResponse;
import com.ttb.top.rbacservice.service.UnmarkRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@EnableLookup
@RestController
@RequiredArgsConstructor
public class UnmarkRoleController {

    private final UnmarkRoleService unmarkRoleService;

    @GetMapping("/unmask-roles")
    ResponseModel<UnmarkRoleResponse> getUnmarkRoles() {
        return ResponseModel.success(unmarkRoleService.getUnmaskRoles());
    }
}
