package com.ttb.top.rbacservice.configuration;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerTypePredicate;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Override
    public void configurePathMatch(PathMatchConfigurer configure) {
        configure.addPathPrefix("/v1/rbac-service",
                HandlerTypePredicate.forBasePackage("com.ttb.top.rbacservice"));
    }
}