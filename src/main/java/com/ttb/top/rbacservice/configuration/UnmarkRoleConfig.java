package com.ttb.top.rbacservice.configuration;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class UnmarkRoleConfig {

    @Value("${unmask.permission.web.name:}")
    private String unmarkPermissionWebName;

    @Value("${unmask.permission.tablet.name:}")
    private String unmarkPermissionTabletName;
}
