package com.ttb.top.rbacservice.configuration;

import java.util.concurrent.Executor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@EnableAsync
@Configuration
public class AsyncConfig {

    @Value("${microsoft-graph.async.executor.corePoolSize}")
    private int corePoolSize;
    @Value("${microsoft-graph.async.executor.maxPoolSize}")
    private int maxPoolSize;
    @Value("${microsoft-graph.async.executor.queueCapacity}")
    private int queueCapacity;
    @Value("${microsoft-graph.async.executor.threadNamePrefix}")
    private String threadNamePrefix;
    @Value("${microsoft-graph.async.executor.waitForTasksToCompleteOnShutdown:true}")
    private boolean waitForTasksToCompleteOnShutdown;
    @Value("${microsoft-graph.async.executor.awaitTerminationSeconds:30}")
    private int awaitTerminationSeconds;

    @Bean(name = "microsoftGraphExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.initialize();
        return executor;
    }
}
