package com.ttb.top.rbacservice.model.feign;

import lombok.Data;

@Data
public class StaffLoginInfoInquiry {
    private String staffId;
    private String tellerId;
    private String azureUserId;
    private String givenNameEn;
    private String surnameEn;
    private String givenNameTh;
    private String surnameTh;
    private String roleNameEn;
    private String roleNameTh;
    private String departmentEn;
    private String departmentTh;
    private String cnRole;
    private String branchCode;
    private String branchNameTh;
    private String branchNameEn;
    private String branchRoleCode;
    private String branchRoleNameTh;
    private String branchRoleNameEn;
    private String timestamp;
}
