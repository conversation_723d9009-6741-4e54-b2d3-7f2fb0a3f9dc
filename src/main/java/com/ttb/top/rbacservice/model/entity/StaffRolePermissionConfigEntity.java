package com.ttb.top.rbacservice.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;


@Data
@Entity(name = "staff_role_permission_config")
@Table(name="staff_role_permission_config")
public class StaffRolePermissionConfigEntity {

    @Id
    @Column(name = "id")
    private Integer id;

    @Column(name = "role_id")
    private String roleId;

    @Column(name = "permission_id")
    private String permissionId;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_datetime")
    private String createdDatetime;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "updated_datetime")
    private String updatedDatetime;

}
