package com.ttb.top.rbacservice.model.microsoft;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GraphApiMemberDetailResponse {

    private String onPremisesSamAccountName;
    private OnPremisesExtensionAttributes onPremisesExtensionAttributes;
    private String msExchExtensionAttribute27;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OnPremisesExtensionAttributes {
        private String extensionAttribute1;
        private String extensionAttribute2;
        private String extensionAttribute3;
        private String extensionAttribute4;
        private String extensionAttribute5;
        private String extensionAttribute6;
        private String extensionAttribute7;
        private String extensionAttribute8;
        private String extensionAttribute9;
        private String extensionAttribute10;
    }
}