package com.ttb.top.rbacservice.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;


@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "movement_staff_list")
public class MovementStaffListEntity {

    @Id
    private ObjectId id;

    @Field(name = "branch_code")
    private String branchCode;

    @Field(name = "sect_code")
    private String sectCode;

    @Field(name = "employee_id")
    private String employeeId;

    @Field(name = "teller_id")
    private String tellerId;

    @Field(name = "level")
    private Integer level;

    @Field(name = "job_code")
    private String jobCode;

    @Field(name = "first_name_th")
    private String firstNameTh;

    @Field(name = "last_name_th")
    private String lastNameTh;

    @Field(name = "job_name_th")
    private String jobNameTh;

    @Field(name = "job_name_en")
    private String jobNameEn;

    @Field(name = "sect_name_th")
    private String sectNameTh;

    @Field(name = "sect_name_en")
    private String sectNameEn;
}
