package com.ttb.top.rbacservice.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CrmPermissionInquiryResponse {

    private String firstname;
    private String lastname;
    private String email;
    private List<StaffLoginInfo.Team> teams;
    private StaffLoginInfo.Profile profile;

}
