package com.ttb.top.rbacservice.model.microsoft;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GraphApiBaseResponse<T> {

    @JsonProperty("@odata.context")
    private String context;
    @JsonProperty("@odata.nextLink")
    private String nextLink;
    private List<T> value;
    private String skipToken;


    public String getSkipToken() {
        if (StringUtils.isBlank(this.nextLink)) {
            return null;
        }
        // Regular expression to match $skiptoken value
        Pattern pattern = Pattern.compile("\\$skiptoken=([^&]+)");
        Matcher matcher = pattern.matcher(this.nextLink);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}