package com.ttb.top.rbacservice.model.feign;

import static com.ttb.top.rbacservice.constant.RbacServiceConstant.ISO_OFFSET_DATETIME_FORMAT;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaffAdminInquiryResponse {

    private String staffId;
    private String tellId;
    private String givenNameEn;
    private String surnameEn;
    private String givenNameTh;
    private String surnameTh;
    private String roleNameEn;
    private String roleNameTh;
    private String departmentEn;
    private String departmentTh;
    private String cnRole;
    private String branchCode;
    private String branchNameTh;
    private String branchNameEn;
    private String branchRoleCode;
    private String branchRoleNameTh;
    private String branchRoleNameEn;
    private String displayNameTh;
    private String displayNameEn;

    @JsonFormat
            (shape = JsonFormat.Shape.STRING, pattern = ISO_OFFSET_DATETIME_FORMAT)
    private Date timestamp;
}
