package com.ttb.top.rbacservice.model.feign.feuam;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.GENERIC_SUCCESS_CODE;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(SnakeCaseStrategy.class)
public class FeuamBaseResponse<T> {

    private Body<T> body;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(SnakeCaseStrategy.class)
    public static class Body<T> {

        private Status apiStatus;
        private T data;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(SnakeCaseStrategy.class)
    public static class Status {

        private String apiCode;
        private String apiMessage;
        private String apiDescription;
    }

    public boolean isSuccess() {
        return Optional.ofNullable(body)
            .map(tBody -> tBody.apiStatus.apiCode.equals(GENERIC_SUCCESS_CODE.getCode()))
            .orElse(Boolean.FALSE);
    }

    public T getBodyData() {
        return Optional.ofNullable(body).map(tBody -> tBody.data).orElse(null);
    }
}
