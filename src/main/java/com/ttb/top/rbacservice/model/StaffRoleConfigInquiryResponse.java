package com.ttb.top.rbacservice.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaffRoleConfigInquiryResponse {

    @Builder.Default
    private List<staffRole> staffRole = new ArrayList<>();

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class staffRole {

        private String roleId;
        private String roleType;
        private String displayRoleType;
        private String roleDescription;
        private String createdBy;
        private LocalDateTime createdDatetime;
        private String updatedBy;
        private LocalDateTime updatedDatetime;

    }

}
