package com.ttb.top.rbacservice.model;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaffListInquiryRequest {

    @NotEmpty
    private List<@NotEmpty String> permissionList;
    @NotEmpty
    private String branchCode;
}
