package com.ttb.top.rbacservice.repository;

import com.ttb.top.rbacservice.model.entity.StaffRolePermissionConfigEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface StaffRolePermissionConfigRepository extends JpaRepository<StaffRolePermissionConfigEntity, Integer> {

    @Query(value = "SELECT role_id, array_agg(permission_id) " +
        "FROM staff_role_permission_config " +
        "WHERE role_id = :requestRoleId GROUP BY role_id",
        nativeQuery = true)
    List<Object[]> findByRoleId(@Param("requestRoleId") String requestRoleId);

    @Query(value = "SELECT DISTINCT role_id FROM staff_role_permission_config WHERE permission_id IN (:permissionIds)",
        nativeQuery = true)
    List<String> findByPermissionIdIn(List<String> permissionIds);

    @Query(value = "SELECT DISTINCT role_id " +
            "FROM staff_role_permission_config " +
            "WHERE permission_id = :permissionIds",
            nativeQuery = true)
    List<String> findByPermissionId(String permissionIds);
}
