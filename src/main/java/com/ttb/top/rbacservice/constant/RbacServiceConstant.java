package com.ttb.top.rbacservice.constant;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.experimental.UtilityClass;

@UtilityClass
public class RbacServiceConstant {

    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().registerModule(new JavaTimeModule());

    public static final String STAFF_ID = "STAFF-ID";
    public static final String ISO_OFFSET_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";
    public static final String COMMA = ",";
    public static final String BRANCH_CODE_001 = "001";
    public static final String BRANCH_CODE_0000000001 = "0000000001 ";

    /*Cache Value*/
    public static final String REDIS_CACHE_MANAGER_NAME = "redisCacheManager";
    public static final String RBAC_CACHE_NAME = "rbac";
    public static final String RBAC_CRM_CACHE_NAME = "rbac_crm";

    /*FEUAM Value*/
    public static final String APP_ID = "app-id";
    public static final String APP_PASSWORD = "app-password";
    public static final String REQUEST_UUID = "request-uuid";
    public static final String REQUEST_DATETIME = "request-datetime";

    /*Keys*/
    public static final String CLIENT_ID = "client_id";
    public static final String CLIENT_SECRET = "client_secret";
    public static final String SCOPE = "scope";
    public static final String GRANT_TYPE = "grant_type";

    /*Azure AD parameter*/
    public static final String ON_PREMISES_SAM_ACCOUNT_NAME = "onPremisesSamAccountName";
    public static final String ON_PREMISES_EXTENSION_ATTRIBUTES = "onPremisesExtensionAttributes";
    public static final String MS_EXCH_EXTENSION_ATTRIBUTE_27 = "msExchExtensionAttribute27";
}
