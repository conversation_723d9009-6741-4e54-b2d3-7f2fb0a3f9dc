package com.ttb.top.rbacservice.feign;

import com.ttb.top.library.requestloghelper.configuration.CustomFeignLoggingConfiguration;
import com.ttb.top.rbacservice.configuration.RestFeignConfig;
import com.ttb.top.rbacservice.model.microsoft.MicrosoftOauth2Token;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
    name = "${microsoft-login.feign.client.name}",
    url = "${microsoft-login.feign.client.url}",
    configuration = {RestFeignConfig.class, CustomFeignLoggingConfiguration.class}
)
public interface MicrosoftLoginClient {

    @CircuitBreaker(name = "microsoftLogin")
    @PostMapping(value = "/{tenantId}/oauth2/v2.0/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    MicrosoftOauth2Token login(
        @NonNull @PathVariable String tenantId,
        @NonNull @RequestBody Map<String, ?> request
    );
}
