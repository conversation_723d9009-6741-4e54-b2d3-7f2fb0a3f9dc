package com.ttb.top.rbacservice.feign;

import com.ttb.top.library.requestloghelper.configuration.CustomFeignLoggingConfiguration;
import com.ttb.top.rbacservice.configuration.RestFeignConfig;
import com.ttb.top.rbacservice.model.microsoft.GraphApiBaseResponse;
import com.ttb.top.rbacservice.model.microsoft.GraphApiGroupDetailResponse;
import com.ttb.top.rbacservice.model.microsoft.GraphApiMemberDetailResponse;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    name = "${microsoft-graph.feign.client.name}",
    url = "${microsoft-graph.feign.client.url}",
    configuration = {RestFeignConfig.class, CustomFeignLoggingConfiguration.class}
)
public interface MicrosoftGraphClient {

    @CircuitBreaker(name = "microsoftGraphGetGroups")
    @GetMapping(value = "/v1.0/groups")
    GraphApiBaseResponse<GraphApiGroupDetailResponse> getGroupsByFilters(
        @NonNull @RequestHeader String authorization,
        @NonNull @RequestParam("$filter") String filter,
        @RequestParam("$skiptoken") String skipToken
    );

    @CircuitBreaker(name = "microsoftGraphGetMembers")
    @GetMapping(value = "/v1.0/groups/{groupId}/members")
    GraphApiBaseResponse<Map<String,Object>> getMembersByGroupId(
        @NonNull @RequestHeader String authorization,
        @NonNull @PathVariable String groupId,
        @RequestParam("$select") String select,
        @RequestParam("$filter") String filter,
        @RequestParam("$skiptoken") String skipToken
    );
}
