package com.ttb.top.rbacservice.feign;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.rbacservice.model.feign.StaffLoginInfoInquiry;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${auth-service.feign.client.name}", url = "${auth-service.feign.client.url}")
public interface AuthServiceClient {
    @GetMapping("/v1/auth-service/staff-login-info/inquiry")
    ResponseModel<StaffLoginInfoInquiry> staffLoginInfoInquiry(@RequestHeader HttpHeaders httpHeaders);
}
