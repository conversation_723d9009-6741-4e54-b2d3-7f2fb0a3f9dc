package com.ttb.top.rbacservice.feign;

import com.ttb.top.library.crmapihelper.config.CrmApiFeignConfig;
import com.ttb.top.library.crmapihelper.model.CrmResponseModel;
import com.ttb.top.library.crmapihelper.model.DecryptCrmResponseBody;
import com.ttb.top.library.crmapihelper.model.EncryptCrmRequestBody;
import com.ttb.top.rbacservice.model.feign.crmssp.UserRetrieveUserInfoRequest;
import com.ttb.top.rbacservice.model.feign.crmssp.UserRetrieveUserInfoResponse;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${feign.crmssp.name}", url = "${feign.crmssp.url}",
        configuration = {CrmApiFeignConfig.class})
public interface CrmsspServiceClient {

    @DecryptCrmResponseBody
    @EncryptCrmRequestBody
    @CircuitBreaker(name = "userRetrieveUserInfo")
    @PostMapping("/user-queue-management/user/retrieveUserInfo")
    CrmResponseModel<UserRetrieveUserInfoResponse> userRetrieveUserInfo(
            @RequestBody UserRetrieveUserInfoRequest request);
}
