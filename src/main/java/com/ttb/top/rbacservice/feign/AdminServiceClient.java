package com.ttb.top.rbacservice.feign;

import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.rbacservice.model.feign.StaffAdminInquiryRequest;
import com.ttb.top.rbacservice.model.feign.StaffAdminInquiryResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${admin-service.feign.client.name}", url = "${admin-service.feign.client.url}")
public interface AdminServiceClient {

    @PostMapping("/v1/admin-service/staff-admin-login-info/inquiry")
    ResponseModel<StaffAdminInquiryResponse> staffAdminInfoInquiry(
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody StaffAdminInquiryRequest staffAdminInquiryRequest);
}
