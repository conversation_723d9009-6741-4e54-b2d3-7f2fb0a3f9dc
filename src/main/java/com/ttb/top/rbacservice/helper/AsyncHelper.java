package com.ttb.top.rbacservice.helper;

import static com.ttb.top.rbacservice.constant.RbacServiceConstant.MS_EXCH_EXTENSION_ATTRIBUTE_27;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.OBJECT_MAPPER;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.ON_PREMISES_EXTENSION_ATTRIBUTES;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.ON_PREMISES_SAM_ACCOUNT_NAME;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.rbacservice.feign.MicrosoftGraphClient;
import com.ttb.top.rbacservice.model.microsoft.GraphApiBaseResponse;
import com.ttb.top.rbacservice.model.microsoft.GraphApiGroupDetailResponse;
import com.ttb.top.rbacservice.model.microsoft.GraphApiMemberDetailResponse;
import com.ttb.top.rbacservice.model.microsoft.GraphApiMemberDetailResponse.OnPremisesExtensionAttributes;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class AsyncHelper {

    private final MicrosoftGraphClient microsoftGraphClient;

    @Value("${microsoft-graph.custom-prefix-attribute}")
    private final String customPrefixAttribute;

    @Async("microsoftGraphExecutor")
    public CompletableFuture<List<GraphApiGroupDetailResponse>> getAllGroupListFromAllPagesAsync(
        String authorization, String filter
    ) {
        List<GraphApiGroupDetailResponse> groupList = new ArrayList<>();
        String skipToken = null;
        do {
            GraphApiBaseResponse<GraphApiGroupDetailResponse> groupsByFilters = microsoftGraphClient.
                getGroupsByFilters(authorization, filter, skipToken);
            groupList.addAll(groupsByFilters.getValue());
            skipToken = groupsByFilters.getSkipToken();
        } while (StringUtils.isNotBlank(skipToken));
        return CompletableFuture.completedFuture(groupList);
    }

    @Async("microsoftGraphExecutor")
    public CompletableFuture<Map<String, List<GraphApiMemberDetailResponse>>> getMembersByGroupIdAsync(
        @NonNull String authorization, @NonNull String groupId, String select, String filter
    ) {
        List<GraphApiMemberDetailResponse> graphApiMemberDetailResponseList = new ArrayList<>();
        String skipToken = null;
        String msExchExtensionAttribute27 = customPrefixAttribute.concat(MS_EXCH_EXTENSION_ATTRIBUTE_27);
        do {
            GraphApiBaseResponse<Map<String, Object>> mapMemberDetailResponses = microsoftGraphClient
                .getMembersByGroupId(
                    authorization,
                    groupId,
                    select,
                    filter,
                    skipToken);

            List<GraphApiMemberDetailResponse> listMemberDetailResponses =
                Optional.ofNullable(mapMemberDetailResponses.getValue()).orElse(Collections.emptyList()).stream()
                    .map(m -> {
                            String extensionAttribute;
                            try {
                                extensionAttribute = OBJECT_MAPPER
                                    .writeValueAsString(m.get(ON_PREMISES_EXTENSION_ATTRIBUTES));
                                return GraphApiMemberDetailResponse.builder()
                                    .onPremisesExtensionAttributes(
                                        OBJECT_MAPPER.readValue(extensionAttribute, OnPremisesExtensionAttributes.class))
                                    .onPremisesSamAccountName(
                                        Optional.ofNullable((String) m.get(ON_PREMISES_SAM_ACCOUNT_NAME)).orElse(null))
                                    .msExchExtensionAttribute27(
                                        Optional.ofNullable((String) m.get(msExchExtensionAttribute27)).orElse(null))
                                    .build();
                            } catch (JsonProcessingException e) {
                                log.error("An error occurred while convert extension attribute", e);
                                throw new GenericException();
                            }
                        }
                    ).collect(Collectors.toList());

            graphApiMemberDetailResponseList.addAll(listMemberDetailResponses);
            skipToken = mapMemberDetailResponses.getSkipToken();
        } while (StringUtils.isNotBlank(skipToken));
        return CompletableFuture.completedFuture(Map.of(groupId, graphApiMemberDetailResponseList));
    }
}
