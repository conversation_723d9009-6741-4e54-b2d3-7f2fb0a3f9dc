package com.ttb.top.rbacservice.helper;

import static com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum.CIRCUIT_BREAKER_CODE;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.CLIENT_ID;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.CLIENT_SECRET;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.COMMA;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.GRANT_TYPE;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.MS_EXCH_EXTENSION_ATTRIBUTE_27;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.ON_PREMISES_EXTENSION_ATTRIBUTES;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.ON_PREMISES_SAM_ACCOUNT_NAME;
import static com.ttb.top.rbacservice.constant.RbacServiceConstant.SCOPE;
import static com.ttb.top.rbacservice.utils.Utils.logCircuitBreakerErrorMessage;

import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.rbacservice.feign.MicrosoftLoginClient;
import com.ttb.top.rbacservice.model.microsoft.GraphApiGroupDetailResponse;
import com.ttb.top.rbacservice.model.microsoft.GraphApiMemberDetailResponse;
import com.ttb.top.rbacservice.model.microsoft.MicrosoftOauth2Token;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import jakarta.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class MicrosoftApiHelper {

    @Value("${microsoft-login.tenant-id}")
    private final String tenantId;
    @Value("${microsoft-login.client-id}")
    private final String clientId;
    @Value("${microsoft-login.client-secret}")
    private final String clientSecret;
    @Value("${microsoft-login.scope}")
    private final String scope;
    @Value("${microsoft-login.grant-type}")
    private final String grantType;

    private final MicrosoftLoginClient microsoftLoginClient;
    private final AsyncHelper asyncHelper;
    @Value("${microsoft-graph.custom-prefix-attribute}")
    private final String customPrefixAttribute;

    private MicrosoftOauth2Token authentication() {
        try {
            return microsoftLoginClient.login(tenantId, Map.of(
                CLIENT_ID, clientId,
                CLIENT_SECRET, clientSecret,
                SCOPE, scope,
                GRANT_TYPE, grantType
            ));
        } catch (Exception ex) {
            log.error("An error occurred while authentication process: ", ex);
            throw ex;
        }
    }

    public List<GraphApiGroupDetailResponse> getGroupsByRoleIds(@NonNull List<String> roleIds) {
        final double maxOperators = 15d;
        try {
            final String authorization = authentication().getAccessTokenWithType();
            if (roleIds.size() > maxOperators) {
                int rounds = (int) Math.ceil(roleIds.size() / maxOperators);
                List<String> roleIdsTemp;
                int indexFrom;
                int indexTo;
                String filter;
                List<String> filters = new ArrayList<>();
                for (int i = 1; i <= rounds; i++) {
                    indexFrom = i == 1 ? 0 : (int) ((i - 1) * maxOperators);
                    indexTo = i == rounds ? roleIds.size() : ((int) (i * maxOperators));
                    roleIdsTemp = roleIds.subList(indexFrom, indexTo);
                    filter = String.format("displayName in ('%s')", String.join("','", roleIdsTemp));
                    filters.add(filter);
                }
                return getAllGroupListFromAllPages(authorization, filters);
            } else {
                final String filter = String.format("displayName in ('%s')", String.join("','", roleIds));
                return getAllGroupListFromAllPages(authorization, List.of(filter));
            }
        } catch (CallNotPermittedException ex) {
            log.error(logCircuitBreakerErrorMessage(ex));
            throw new GenericException(CIRCUIT_BREAKER_CODE);
        } catch (Exception ex) {
            log.error("An error occurred while retrieving groups from Graph API: ", ex);
            throw new GenericException();
        }
    }

    private List<GraphApiGroupDetailResponse> getAllGroupListFromAllPages(
        String authorization,
        List<String> filters
    ) {
        List<CompletableFuture<List<GraphApiGroupDetailResponse>>> futures = filters.stream()
            .map(filter -> asyncHelper.getAllGroupListFromAllPagesAsync(authorization, filter))
            .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        List<GraphApiGroupDetailResponse> allGroupList = new ArrayList<>();
        futures.forEach(listCompletableFuture -> allGroupList.addAll(listCompletableFuture.join()));

        return allGroupList;
    }

    public Map<String, List<GraphApiMemberDetailResponse>> getMembersByGroupId(
        @NonNull @NotEmpty List<String> groupIds
    ) {
        try {
            final String authorization = authentication().getAccessTokenWithType();
            final StringJoiner stringJoiner = new StringJoiner(COMMA)
                .add(ON_PREMISES_SAM_ACCOUNT_NAME)
                .add(ON_PREMISES_EXTENSION_ATTRIBUTES);

            if(Objects.nonNull(customPrefixAttribute)) {
                stringJoiner.add(customPrefixAttribute.concat(MS_EXCH_EXTENSION_ATTRIBUTE_27));
            }

            final String query = stringJoiner.toString();

            List<CompletableFuture<Map<String, List<GraphApiMemberDetailResponse>>>> futures = groupIds.stream()
                .map(groupId -> asyncHelper.getMembersByGroupIdAsync(authorization, groupId, query, null))
                .toList();

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            Map<String, List<GraphApiMemberDetailResponse>> mapMembersByGroup = new HashMap<>();
            futures.forEach(listCompletableFuture -> {
                Map<String, List<GraphApiMemberDetailResponse>> members = listCompletableFuture.join();
                String groupId = members.keySet().stream().findFirst().get();
                mapMembersByGroup.put(groupId, members.get(groupId));
            });

            return mapMembersByGroup;
        } catch (CallNotPermittedException ex) {
            log.error(logCircuitBreakerErrorMessage(ex));
            throw new GenericException(CIRCUIT_BREAKER_CODE);
        } catch (Exception ex) {
            log.error("An error occurred while retrieving members from Graph API: ", ex);
            throw new GenericException();
        }
    }
}
