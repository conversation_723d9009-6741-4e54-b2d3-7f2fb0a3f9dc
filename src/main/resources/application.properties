spring.application.name=rbac-service

lookup.type.name=top.response.status
spring.autoconfigure.exclude=org.springframework.boot.actuate.autoconfigure.metrics.cache.CacheMetricsAutoConfiguration

#Microsoft Credentials
microsoft-login.client-id=client_id
microsoft-login.client-secret=client_secret
microsoft-login.scope=scope
microsoft-login.grant-type=grant_type
microsoft-login.tenant-id=tenant_id

#Microsoft Online & Graph API
microsoft-login.feign.client.name=microsoft-login
microsoft-login.feign.client.url=https://login.microsoftonline.com
microsoft-graph.feign.client.name=microsoft-graph
microsoft-graph.feign.client.url=https://graph.microsoft.com

#auth-service
auth-service.feign.client.name = auth-service
auth-service.feign.client.url = https://

#admin-service
admin-service.feign.client.name = admin-service
admin-service.feign.client.url = https://

#feuam-service
feuam-service.feign.client.name=feuam-service
feuam-service.feign.client.url=http://karate.local
feuam.credentials.app-id=app-id
feuam.credentials.app-password=app-password

#crmssp
feign.crmssp.name=crmssp-service
feign.crmssp.url=${env.crm.url}

#postgres
spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=test
spring.datasource.hikari.schema=top_rbac_service
spring.kafka.producer.bootstrap-servers=https://
spring.kafka.producer.security.protocol=
spring.kafka.producer.sasl.jaas.config=
spring.kafka.producer.sasl.mechanism=
azure.eventhub.password=
activity.log.topic=
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
#Hibernate ddl auto (create, create-drop, validate, update)
spring.jpa.hibernate.ddl-auto=none

#Redis
spring.data.redis.host=redis-a0551-top-dev.redis.cache.windows.net
spring.data.redis.port=6380
spring.data.redis.password=VttZDk3OXk8HXRG3m3Xt4B63F0rAAOUucAzCaN02Dtk=
spring.data.redis.ssl.enabled=true
local.cache.enable=true

#Redis TTL config
custom.cache.expire.staff_role_permission_config_role_id=86400
custom.cache.expire.staff_role_config=86400
custom.cache.expire.rbac=86400
custom.cache.expire.rbac_crm=1800

# circuit breaker
circuitbreaker.instances.list=microsoftLogin,microsoftGraphGetGroups,microsoftGraphGetMembers,userRetrieveUserInfo
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.sliding-window-type=time_based
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.sliding-window-size=20
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.permitted-number-of-calls-in-half-open-state=3
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.minimum-number-of-calls=3
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.failure-rate-threshold=50
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.slow-call-rate-threshold=30
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.record-failure-predicate=com.ttb.top.library.circuitbreakerhelper.predicate.CustomPredicate
# circuit breaker - microsoftLogin
circuitbreaker.instances.microsoftLogin.enabled=true
resilience4j.circuitbreaker.instances.microsoftLogin.base-config=defaultCircuitBreaker
# circuit breaker - microsoftGraphGetGroups
circuitbreaker.instances.microsoftGraphGetGroups.enabled=true
resilience4j.circuitbreaker.instances.microsoftGraphGetGroups.base-config=defaultCircuitBreaker
# circuit breaker - microsoftGraphGetMembers
circuitbreaker.instances.microsoftGraphGetMembers.enabled=true
resilience4j.circuitbreaker.instances.microsoftGraphGetMembers.base-config=defaultCircuitBreaker
# circuit breaker - userRetrieveUserInfo
resilience4j.circuitbreaker.instances.userRetrieveUserInfo.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.userRetrieveUserInfo.record-failure-predicate=com.ttb.top.rbacservice.predicate.CrmPredicate
resilience4j.circuitbreaker.instances.userRetrieveUserInfo.permitted-number-of-calls-in-half-open-state=3
resilience4j.circuitbreaker.instances.userRetrieveUserInfo.failure-rate-threshold=100
resilience4j.circuitbreaker.instances.userRetrieveUserInfo.minimum-number-of-calls=3
resilience4j.circuitbreaker.instances.userRetrieveUserInfo.slow-call-rate-threshold=30
circuitbreaker.instances.userRetrieveUserInfo.enabled=true

# Feign client
spring.cloud.openfeign.okhttp.enabled=true

masking.config.file=masking-pattern.xml

#Async executor
microsoft-graph.async.executor.corePoolSize=4
microsoft-graph.async.executor.maxPoolSize=8
microsoft-graph.async.executor.queueCapacity=50
microsoft-graph.async.executor.threadNamePrefix=AsyncMicrosoftGraph-
microsoft-graph.async.executor.waitForTasksToCompleteOnShutdown=true
microsoft-graph.async.executor.awaitTerminationSeconds=30

microsoft-graph.custom-prefix-attribute=extension_69b996c4516948ba83bee4ee403fb5de_

#Unmask role permission
unmask.permission.web.name=WEB_CUST360_PERSONAL_INFO_VIEW
unmask.permission.tablet.name=CUST360_PERSONAL_INFO_VIEW
