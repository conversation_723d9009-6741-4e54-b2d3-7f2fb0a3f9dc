buildscript {
	ext {
		springBootVersion = '3.4.3'
		springCloudVersion = '2024.0.0'
		springCloudAzureDependenciesVersion = '5.20.0'
		ttbVersion = '5.0.70-dev.00754'
	}
	repositories {
		maven {
			url 'https://nexus.tmbbank.local:8081/repository/oneapp'
			credentials {
				username = mavenUser
				password = mavenPassword
			}
		}
		maven {
			url "https://nexus.tmbbank.local:8081/repository/plugins.gradle/"
			credentials {
				username = mavenUser
				password = mavenPassword
			}
		}
	}

	dependencies {
		classpath "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
		classpath "com.palantir.gradle.docker:gradle-docker:0.35.0"
		classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:4.4.1.3373"
	}
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'com.palantir.docker'
apply plugin: 'jacoco'
apply plugin: 'project-report'
apply plugin: "org.sonarqube"
apply plugin: "groovy"

group = 'com.ttb.top'
version = '1.0.0'
sourceCompatibility = '17'

jar {
	enabled = false
	archiveClassifier = ''
}

if (project.hasProperty('projVersion')) {
	project.version = project.projVersion
} else {
	project.version = '1.0.0'
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	maven {
		url 'https://nexus.tmbbank.local:8081/repository/oneapp'
		credentials {
			username = mavenUser
			password = mavenPassword
		}
	}
}

springBoot {
	buildInfo()
}

dependencies {
	//TTB Dependencies for Library
	implementation(platform("com.ttb.top.library:ttb-dependencies-helper:${ttbVersion}"))
	annotationProcessor(platform("com.ttb.top.library:ttb-dependencies-helper:${ttbVersion}"))
	implementation 'com.ttb.top.library:lookup-helper'
	implementation 'com.ttb.top.library:common-model'
	implementation 'com.ttb.top.library:exception-model'
	implementation 'com.ttb.top.library:httpheader-helper'
	implementation 'com.ttb.top.library:utility-helper'
	implementation 'com.ttb.top.library:request-log-helper'
	implementation 'com.ttb.top.library:redis-cache-helper'
	implementation 'com.ttb.top.library:circuit-breaker-helper'
	implementation 'com.ttb.top.library:mongo-helper'
	implementation 'com.ttb.top.library:crm-api-helper'

	//for crm-helper
	implementation 'com.auth0:java-jwt'
	implementation 'org.bouncycastle:bcprov-jdk18on'

	//for lookup-helper
	implementation 'org.ehcache:ehcache'
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.aspectj:aspectjweaver:1.9.9.1'
	implementation 'org.apache.commons:commons-lang3'

	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-aop'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	implementation 'io.micrometer:micrometer-registry-prometheus'
	runtimeOnly 'io.micrometer:micrometer-registry-prometheus'
	implementation 'org.springframework.kafka:spring-kafka'
	implementation 'io.github.resilience4j:resilience4j-spring-boot3'
	implementation 'io.github.resilience4j:resilience4j-all'

	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.0'
	implementation 'org.springframework:spring-context-support'

	//lombok and mapstruct
	implementation 'org.projectlombok:lombok'
	implementation 'org.mapstruct:mapstruct'
	annotationProcessor 'org.projectlombok:lombok'
	annotationProcessor 'org.mapstruct:mapstruct-processor'
	annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

	//redis
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'
	implementation 'com.github.ben-manes.caffeine:caffeine'

	//Feign HttpClient
	implementation 'io.github.openfeign:feign-okhttp:13.5'

	//Postgres
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.postgresql:postgresql'

	//Mongodb
	implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'

	//testing
	testImplementation 'org.spockframework:spock-spring'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.spockframework:spock-core'
	testImplementation 'org.instancio:instancio-junit'
}

dependencyManagement {
	imports {
		mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
	}
}

tasks.named('test') {
	useJUnitPlatform()
}

docker {
	name "com.ttb.top/${project.name}:${project.version}"
	dockerfile file('Dockerfile')
	files jar.archiveFile
	buildArgs(['JAR_FILE': "${jar.archiveFileName}"])
}

tasks.getByPath('dockerPrepare').dependsOn('bootJar')
tasks.getByPath('dockerPrepare').dependsOn('jar')
tasks.getByPath('docker').dependsOn('build')

jacoco {
	toolVersion = "0.8.11"
}

jacocoTestReport {
	reports {
		html.required = true
		xml.required = true
		csv.required = true
	}
}

sonarqube {
	if (System.getProperty("sonar.host.url").equals(null)) {
		properties {
			System.setProperty('sonar.host.url', 'http://localhost:9000')
		}
	}
	properties {
		property 'sonar.coverage.exclusions', '**/configuration/**, **/config/**, **/model/**, **/dto/**, **/wsdl/**, **/entity/**, **/utils/**, **/constant/*, **/repository/*, **/exception/*, **/RbacServiceApplication.java'
	}
	properties {
		property 'sonar.exclusions', '**/configuration/**, **/config/**, **/model/**, **/dto/**, **/wsdl/**, **/entity/**, **/repository/**, **/utils/**, **/RbacServiceApplication.java'
	}
}

test.finalizedBy jacocoTestReport
